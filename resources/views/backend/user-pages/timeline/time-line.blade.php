@extends('backend.layout.master')
@section('page-header')
@endsection
<style>
   .tracking .card {
   z-index: 0;
   background-color: #ECEFF1;
   padding-bottom: 20px;
   margin-top: 20px;
   border-radius: 10px
   }
   .tracking .top {
   padding-top: 40px;
   padding-left: 13% !important;
   padding-right: 5% !important
   }
   #progressbar {
   margin-bottom: 20px;
   overflow: hidden;
   color: #455A64;
   padding-left: 0px;
   margin-top: 20px
   }
   #progressbar li {
   list-style-type: none;
   font-size: 13px;
   width: 20%;
   float: left;
   position: relative;
   font-weight: 400
   }
   #progressbar .step0:before {
   font-family: FontAwesome;
   content: "\f10c";
   color: #fff
   }
   #progressbar li:before {
   width: 20px;
   height: 20px;
   line-height: 32px;
   display: block;
   font-size: 10px;
   background: #C5CAE9;
   border-radius: 50%;
   margin: auto;
   padding: 0px;
   }
   #progressbar li:after {
   content: '';
   width: 100%;
   height: 3px;
   background: #C5CAE9;
   position: absolute;
   left: 0;
   top: 14px;
   z-index: -1;
   }
   #progressbar li:last-child:after {
   border-top-right-radius: 10px;
   border-bottom-right-radius: 10px;
   position: absolute;
   left: -50%
   }
   #progressbar li:nth-child(2):after,
   #progressbar li:nth-child(3):after,
   #progressbar li:nth-child(4):after {
   left: -50%
   }
   #progressbar li:first-child:after {
   border-top-left-radius: 10px;
   border-bottom-left-radius: 10px;
   position: absolute;
   left: 50%
   }
   #progressbar li:last-child:after {
   border-top-right-radius: 10px;
   border-bottom-right-radius: 10px
   }
   #progressbar li:first-child:after {
   border-top-left-radius: 10px;
   border-bottom-left-radius: 10px
   }
   #progressbar li.active:before,
   #progressbar li.active:after {
   background: #651FFF
   }
   #progressbar li.active:before {
   font-family: FontAwesome;
   content: "\f00c"
   }
   .tracking .icon {
   width: 60px;
   height: 60px;
   margin-right: 15px
   }
   .tracking .icon-content {
   padding-bottom: 20px
   }
   .accordion {
   font-size: 1rem;
   margin: 0 auto;
   border-radius: 5px;
   }
   .accordion-header,
   .accordion-body {
   background: white;
   }
   .accordion-header {
   padding: 1em 3em;
   background: #fff;
   cursor: pointer;
   transition: all .3s;
   font-style: normal;
   font-weight: 500;
   font-size: 12px;
   line-height: 25px;
   font-feature-settings: 'pnum' on, 'lnum' on;
   color: #616161;
   border-radius: 8px
   }
   .accordion__item {
   border-radius: 8px;
   box-shadow: 0px 4px 15px rgba(31, 61, 91, 0.06);
   margin-bottom: 20px;
   }
   .accordion__item .accordion__item {
   border-bottom: 1px solid rgba(0, 0, 0, 0.08);
   }
   .accordion-header:hover {
   background: #e6e6e6;
   position: relative;
   z-index: 5;
   border-radius: 8px;;
   }
   .accordion-body {
   background: #fcfcfc;
   color: #353535;
   display: none;
   }
   .accordion-body__contents {
   padding: 3em;
   font-size: 11px;
   font-style: normal;
   font-weight: normal;
   line-height: 150%;
   color: #4A5F75;
   }
   .accordion__item.active:last-child .accordion-header {
   border-radius: none;
   }
   .accordion:first-child > .accordion__item > .accordion-header {
   border-bottom: 1px solid transparent;
   }
   .accordion__item > .accordion-header:after {
   content: "";
   font-family: IonIcons;
   font-size: 1.2em;
   float: right;
   position: relative;
   top: -2px;
   transition: .3s all;
   transform: rotate(0deg);
   background: url({{url('images/plus.svg')}});
   width: 20px;
   height: 20px;
   }
   .accordion__item.active .accordion-header:after {
   content: "";
   font-family: IonIcons;
   font-size: 1.2em;
   float: right;
   position: relative;
   top: -2px;
   transition: .3s all;
   transform: rotate(0deg);
   background: url({{url('images/minus.svg')}});
   width: 20px;
   height: 20px;
   }
   .accordion__item.active > .accordion-header:after {
   transform: rotate(-180deg);
   }
   .accordion__item.active .accordion-header {
   background: #e6e6e6;
   border-radius: 8px 8px 0px 0px;
   color: #616161
   }
   .accordion__item .accordion__item .accordion-header {
   background: #f1f1f1;
   color: #353535;
   }
   .ul-scroll {
   height: 200px;
   margin: 0 auto;
   overflow: hidden;
   }
   .ul-scroll:hover {
   overflow-y: scroll;
   }
   .ul-scroll::-webkit-scrollbar {
   width: 5px;
   }
   /* Track */
   .ul-scroll::-webkit-scrollbar-track {
   background: #EBF4FF
   }
   /* Handle */
   .ul-scroll::-webkit-scrollbar-thumb {
   background: #FC5E6C;
   border-radius: 26px;
   max-height: 50%;
   height: 50%;
   }
   .ul-scroll::-webkit-scrollbar-button {
   height: 50px;
   }
   /* Handle on hover */
   .tracking-scroll::-webkit-scrollbar-thumb:hover {
   }
   .main-wrapper-change{
   padding:0px !important;
   margin-top:0px !important;
   background-color:#fff !important;
   }
   .card {
   border: 1px solid #e9e9ef !important;
   }
   .timeline-new .timeline-action a {
   border: 0;
   background: #FFE0E2;
   width: 35px;
   height: 35px;
   display: inline-flex;
   align-items: center;
   justify-content: center !important;
   padding: 0 !important;
   color: #FE5F6C;
   }
   i.fa.fa-trash {
   color: #FE5F6C;
   }
   .timeline-new .timeline-header {
   background: none;
   }
   .panel-title.new-panel {
   background: #29CC97 !important;
   }
   .timeline-title:has(> .fa-edit)  {
   background: #CCECFF !important;
   }
   a.timeline-title.edit-timeline {
   background: #CCECFF;
   color: #0692E3 !important;
   }
   .image-div {
   width:120px;
   height:120px;
   margin: 0 auto;
   }
   .timeline-header.tl-panel-hdr-lght-fb-blue, .timeline-new .timeline-header {
   background: none !important;
   }
   .timeline-new li > .timeline-panel .panel-title {
   width: 31px !important;
   padding: 5px !important;
   font-size: 13px !important;
   font-weight: 600 !important;
   /* border-radius: 5px; */
   }
   .timeline-badge {
   border: none !important;
   color: #fff !important;
   background: #29cc97 !important;
   }
   .timeline-badge.tl-badge-bdr-blue {
   border: none !important;
   background: #0692E3 !important;
   color: #fff !important;
   }
   .timeline-badge.tl-badge-bdr-fb-blue {
   color: #fff !important;
   border: none !important;
   background: #00bcd4 !important;
   }
   .timeline-new .timeline-header .user-img {
   width: 25px !important;
   height: 25px !important;
   font-size: 10px !important;
   font-weight: 400 !important;
   }
   .timeline-new .timeline-badge {
   width: 32px !important;
   height: 32px !important;
   }
   .timeline-new .timeline-action a {
   width: 27px;
   height: 27px;
   }
   .tl-new-top-sec .tl-user-info .tl-user-details {
   padding: 0 20px !important;
   }
   .tl-new-top-sec .tl-user-info .tl-user-adrs {
   padding: 5px 20px !important;
   }
   /* .tl-new-top-sec .tl-user-info .tl-details ul li {
   padding: 10px 20px;
   } */
   .tl-new-top-sec .tl-user-info .tl-details {
   padding: 0 !important;
   }
   .tl-new-top-sec .tl-user-info {
   height: auto !important;
   }
   .tl-new-top-sec .tl-user-info .tl-user-adrs ul li i {
   width: 23px !important;
   height: 23px;
   display: inline-flex;
   align-items: center;
   justify-content: center;
   border-radius: 50%;
   background: #95A6BD;
   color: #fff;
   }
   .tl-new-top-sec .tl-user-info .tl-user-adrs ul li {
   padding: 8px 0;
   font-size: 13px;
   font-weight: 500;
   line-height: 17px;
   }
   .tl-new-top-sec .tl-user-info .tl-details ul li p {
   font-weight: 400;
   margin: 0;
   color: #4e4e4e;
   } 
   .tl-new-top-sec .tl-user-info .tl-details ul li .select2-container {
   text-align: left;
   font-size: 12px;
   /* background: rgba(251, 147, 65, 0.1) !important; */
   border-bottom: 1px solid #dee2e6;
   /* box-sizing: border-box; */
   border-radius: 0px;
   }
   .select2-selection__rendered{padding-left:0px !important;border-radius: 0px !important;}
   .tl-new-top-sec .tl-user-info .tl-details ul li .select2-container--default .select2-selection--single {
   border: 0;
   height: 20px;
   margin-bottom: 0;
   border-radius: 100px;
   }
   .tl-user-info .select2-container--default .select2-selection--single .select2-selection__arrow b {
   border-color: #FB9341 transparent transparent transparent !important;
   }
   .tl-user-info .select2-container--default .select2-selection--single .select2-selection__rendered {
   /* color: #FB9341 !important; */
   text-transform:capitalize !important
   }
   .tl-new-top-sec .tl-user-info .tl-details ul li .select2-container--default .select2-selection--single {
   background: #fff;
   border:1px solid #e4e4e4;
   }
   .ordertag
   {
   color:black;
   border: 0;
   padding: 0.5rem 0.75rem; 
   border-top-left-radius: 0.25rem;
   border-top-right-radius: 0.25rem;
   }
   .tl-new-top-sec .tl-user-info .tl-details ul li label:first-child {
   padding-right: 20px;
   }
   ul.timeline-opt {
   list-style: none;
   display: flex;
   justify-content: center;
   margin-bottom: 10px;
   }
   ul.timeline-opt a {
   padding: 0;
   border: none;
   }
   .timeline-new {
   padding-top: 0 !important;
   }
   label.input-boxes input, label.input-boxes select{
   border: 0;
   border-bottom: 1px solid #ced4da;
   border-radius: 0;
   margin-bottom:0;
   }
   label.input-boxes {
   width: 60% !important;
   }
   .gotoDeal {
   padding: 0px 0px !important;
   font-weight: 700 !important;
   text-transform: none !important;
   border-width: 0px;
   border-style: none;
   border-color: rgb(255, 255, 255);
   border-image: none;
   border-radius: 0px;
   }
   .tel-a{
   padding: 0px 0px !important;
   color: black !important;
   font-weight: 500 !important;
   }
   .edit-task-modal .timeline-tab-inner input.datepicker.timeline-value-v2 {
   z-index: 9999;
   }

   button.ui-datepicker-trigger{
      display: none;
    }


   .timeline-body a{
      display: contents !important;
      padding: 0px !important;
      font-weight: 500 !important;
      text-transform: none !important;
      text-decoration: none !important;
      color: #0740a9 !important;
   }
   .deals-sidebar .info-lists .select2-container--default .select2-selection--multiple .select2-selection__rendered {
    max-height: 65px !important;
    overflow: auto;
}

body.modal-open {
    overflow: hidden !important;
}
.enquiry-container {
  padding: 6px;
}

.enquiry-header {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.enquiry-icon {
  margin-right: 5px;
}

.enquiry-list {
  list-style-type: none;
  padding: 0;
  margin-top: 10px;
  position: absolute;
   background: #efece8;
   padding: 4px;
   border-radius: 3px;
}

.enquiry-list a{
   border-radius: 0px !important;
}


</style>
@section('content')
@include('backend.user-pages.timeline.edit_profile')
<link rel="stylesheet" href="{{url('css/themes/bootstrap-datetimepicker.min.css')}}">
<link rel="stylesheet" href="{{url('css/pageloader/style.min.css')}}">
<script type="text/javascript" src="https://ajax.googleapis.com/ajax/libs/jquery/1.9.0/jquery.min.js"></script>
<link rel="stylesheet"  href="{{url('css/pageloader/style.min.css')}}">
<link rel="stylesheet" href="{{url('backend/css/deals.css')}}">
<style>
  /* #msform input[type="text"], #msform select {
      min-height: 0px !important;
      min-height: 45px;
   } */
   #loadingDivWeb {
      position: absolute;
      width: 100%;
      height: 100%;
      background-color: rgba(233, 233, 233, 0.9);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 999;
      min-height: 490px;
   }
   #loadingDivWeb img {
      width: 180px;
      height: 180px;
   }
</style>
<main class="main-wrapper main-wrapper-change">
      <div class="d-none" id="loadingDivWeb"><img src="/backend/call_web.gif" alt="Loading......."></div>
   <div class="layout-wrapper">
      <!--   / Side menu included /  -->
      <input type="hidden" value="{{$enquiry->pk_int_enquiry_id}}" id="enqId">
      <div class="content-section bg-white pt-0 pl-3 pr-3 pb-0">
         <!--  /Your content goes here/ -->
         <section class="tl-new-top-sec deal-popup-v1 pt-0">
            <div class="row">
               @include('backend.user-pages.timeline.side-bar')
               <div class="col-lg-9 rt-deal-detail">
                  <div class="">
                     <div class="timeline-new-tab-sec pb-0">
                        <div class="row">
                           <div class="col-sm-12">
                              <ul class="nav nav-tabs mb-2" id="myTab" role="tablist">
                                 <div class="slider"></div>
                                 <li class="nav-item" style="margin-right:40px"> 
                                    <a class="nav-link active" id="l-activity-tab" data-toggle="tab" href="#lead-activity" role="tab"
                                       aria-controls="log" aria-selected="false" style="padding-left:0px;padding-right:0px">
                                    <i class="fa fa-flag"></i>
                                    Activity
                                    </a>
                                 </li>
                                 <li class="nav-item" style="margin-right:40px"> 
                                    <a class="nav-link" id="log-tab" data-toggle="tab" href="#log" role="tab"
                                       aria-controls="log" aria-selected="false" style="padding-left:0px;padding-right:0px">
                                    <i class="fa fa-plus"></i>
                                    Log Note
                                    </a>
                                 </li>
                                
                                 <li class="nav-item" style="margin-right:40px"> 
                                    <a class="nav-link" id="task-tab" data-toggle="tab" href="#task" role="tab"
                                       aria-controls="task" aria-selected="false" style="padding-left:0px;padding-right:0px">
                                    <i class="fa fa-calendar"></i>
                                    Task
                                    </a>
                                 </li>
                  
                                 @if($enquiry->vchr_customer_email!=NULL)
                                 <li class="nav-item" style="margin-right:40px"> 
                                    <a class="nav-link" id="email-tab" data-toggle="tab" href="#emailsend"
                                       role="tab" aria-controls="email" aria-selected="false" style="padding-left:0px;padding-right:0px">
                                    <i class="fa fa-envelope-o"></i>
                                    Email
                                    </a>
                                 </li>
                                 @endif
                                 <li class="nav-item" style="margin-right:40px"> 
                                    <a class="nav-link" id="deal-tab" data-toggle="tab" href="#deal"
                                       role="tab" aria-controls="deal" aria-selected="false" style="padding-left:0px;padding-right:0px">
                                    <i class="fa fa-fire"></i>
                                    {{ getDealName(true) }}
                                    </a>
                                 </li>
                                
                                 <li class="nav-item" style="margin-right:40px">
                                    <a class="nav-link" id="order-tab" target="_blank" href="/user/sales/orders/create/{{$enquiry->pk_int_enquiry_id}}" role="tab"
                                       aria-controls="order" aria-selected="false" style="padding-left:0px;padding-right:0px">
                                    <i class="fa fa-shopping-cart"></i>
                                    Order</a>
                                 </li>
                              </ul>
                           </div>
                        </div>


                        <div class="tab-content" id="myTabContent">
                           {{-- Create Note --}}
                           <!-- @include('backend.user-pages.timeline.tab-note') -->
                           {{-- Create Log --}}
                           @include('backend.user-pages.timeline.tab-log')
                           {{-- Create Image --}}
                           @include('backend.user-pages.timeline.tab-image')
                           {{-- Create Task --}}
                           @include('backend.user-pages.timeline.tab-task')
                           {{-- Create Schedule --}}
                           @include('backend.user-pages.timeline.tab-schedule')
                           {{-- Create Email --}}
                           @include('backend.user-pages.timeline.tab-email-send')
                           {{-- Create Details --}}
                           @include('backend.user-pages.timeline.tab-details')
                           {{-- Additional Details --}}

                           @include('backend.user-pages.timeline.tab-deal')


                           <div class="tab-pane fade show active tab-sec-col" id="lead-activity" role="tabpanel" aria-labelledby="l-activity-tab">
                              <div class="row m-0 ">
                                 <div class="card w-100" style="border:none !important">
                                    @if(true)
                                    <div class="activity-tabs" >
                                       <div class="row">
                                          <div class="col-sm-6">
                                             <p class="h-label">Latest activities</p>
                                          </div>
                                          <div class="col-sm-6" style="display: flex; justify-content: flex-end;">
                                            
                                          </div>
                                       </div>
                                       @php $new_lead=0; @endphp
                                       <ul class="timeline-new activity-timeline" style="border:none !important">
                                          @foreach($enquiryFollowups as $month=>$enquiryFollowup)
                                          <li class="timeline-period">{{$month}}</li>
                                          @foreach($enquiryFollowup as $ef)
                                          @if($ef->task_category_id && (($ef->task_category_id==2 && \Getlead\Rolespermissions\Models\RolePermission::hasPermission('timeline.view.calls')) || ($ef->task_category_id!=2 && \Getlead\Rolespermissions\Models\RolePermission::hasPermission('timeline.view.tasks'))))
                                          <li class="timeline-inverted">
                                             <div class="timeline-badge">
                                                <i class="fa  @if($ef->task_category_id==2)fa-phone @else fa-file-text @endif"style="margin-top: 10px;"></i>
                                             </div>
                                             <div class="timeline-panel">
                                                <div class="panel-title" style="@if($ef->status==1)
                                                   background: green;
                                                   @elseif($ef->status==0 && $ef->scheduled_date<Carbon\Carbon::today())
                                                   background: red;
                                                   @else
                                                   background: orange;
                                                   @endif">@if($ef->task_category_id==2)Call @else Task @endif</div>
                                                <div class="timeline-header">
                                                   <div>
                                                      <div class="user-img profileImage"
                                                         style=" background: #D36DDD;color: #fff;">
                                                         @if($ef->assigned_by_user)
                                                         {{ strtoupper( substr($ef->assigned_by_user, 0, 1))}}
                                                         @endif
                                                      </div>
                                                   </div>
                                                   <div class="timeline-heading">
                                                      <div class="col-md-9">
                                                         <p class="timeline-title"><span class="user">{{ $ef->assigned_by_user }}</span> added
                                                            a new <span class="task">
                                                            @if($ef->task_category_id==2) 
                                                               @if($ef->campaign_id) Campaign Task @else Call Task @endif
                                                            @else 
                                                            @if($ef->task_category_id!=2)
                                                            {{$ef->task_category}}
                                                            task
                                                            @endif  
                                                            @endif
                                                            </span> 
                                                            for 
                                                            <span class="assigne"> {{$ef->assigned_user_name}}</span>
                                                         </p>
                                                         <p class="m-0">
                                                            {{\App\Common\Variables::changeDateFormat($ef->created_at)}}at {{\App\Common\Variables::changeTimeFormat($ef->created_at)}}
                                                         </p>
                                                         <div class="timeline-body">
                                                            <h6 class="title"></h6>
                                                            @if($ef->status==1)
                                                            <p class="content-block content-block-done">
                                                               <i class="fa fa-duotone fa-check"></i>
                                                               <span>{!!$ef->description!!}</span>
                                                            </p>
                                                            @else
                                                            <p class="content-block">
                                                               <i class="fa fa-duotone fa-check"></i>
                                                               <span>{!!$ef->description!!}</span>
                                                            </p>
                                                            @endif
                                                         </div>
                                                      </div>
                                                      <div class="col-md-3 pr-0">
                                                         <div class="timeline-action">
                                                            <div class="dropdown show">
                                                               <a class="btn dropdown-toggle" href="#" role="button" id="dropdownMenuLink" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                                               <i class="fa fa-circle" aria-hidden="true"></i>
                                                               <i class="fa fa-circle" aria-hidden="true"></i>
                                                               <i class="fa fa-circle" aria-hidden="true"></i>   
                                                               </a>
                                                               <div class="dropdown-menu" aria-labelledby="dropdownMenuLink">
                                                                 
                                                                  @if($ef->status!=1)
                                                                  <a class="dropdown-item" href="javascript:void(0)" id="reschedule_plan" task-id={{$ef->id}}>
                                                                  <img src="/backend/images/refresh.png" alt="Getlead" title="Task Reschedule">&nbsp;Reschedule Task
                                                                  </a>
                                                                  @endif
                                                                  @if($ef->task_category_id != 2 &&  $ef->status!=1)
                                                                  <a class="dropdown-item" href="javascript:void(0)" id="task_complete" task-id={{$ef->id}}>
                                                                  <img src="/backend/images/task-finish.svg" alt="Getlead" title="Task Complete">&nbsp;Complete 
                                                                  </a>
                                                                  @endif
                                                                  <a class="dropdown-item" href="#" id="task_edit_btn" data-taskid="{{$ef->id}}">
                                                                  <img src="/backend/images/deals/task.svg" data-taskid="{{$ef->id}}" alt="Getlead">&nbsp;Edit
                                                                  </a>
                                                                  <a href="" class="dropdown-item timeline-title delete-task" data-task-id="{{$ef->id}}">
                                                                  <img src="/backend/images/deals/delete-2.svg" alt="Getlead">&nbsp;Delete
                                                                  </a>
                                                               </div>
                                                            </div>
                                                         </div>
                                                      </div>
                                                   </div>
                                                </div>
                                                {{-- check data --}}
                                                <div class="task-schedule-date">
                                                   <p><img src="/backend/images/deals/calender.svg">Scheduled Date:&nbsp;<span>
                                                      @if ($ef->scheduled_date == '0000-00-00 00:00:00')
                                                      No date
                                                      @else
                                                      {{ $ef->scheduled_date ? Carbon\Carbon::parse($ef->scheduled_date)->format('j M Y  h:i A') : ''}}
                                                      @endif
                                                      </span>
                                                   </p>
                                                   <p class="content-block">Status:&nbsp;
                                                      @if($ef->status==1)
                                                      <span style="color: #34BFA3 !important">Completed</span>
                                                      @elseif($ef->status==0 && $ef->scheduled_date<Carbon\Carbon::today())
                                                      <span>OverDue</span>
                                                      @else
                                                      <span style="color: #F2994A !important">Pending</span>
                                                      @endif
                                                   </p>
                                                </div>
                                             </div>
                                          </li>
                                          {{--Start Deal listing --}}
                                          @elseif($ef->pk_int_deal_id && \Getlead\Rolespermissions\Models\RolePermission::hasPermission('timeline.view.deals'))
                                          <li class="timeline-inverted">
                                             <div class="timeline-badge">
                                                <i class="fa fa-fire " style="margin-top: 10px;"></i>
                                             </div>
                                             <div class="timeline-panel">
                                                <a href="{{url('user/deal-timeline').'/'.$ef->pk_int_deal_id}}" class="gotoDeal">
                                                   <div class="panel-title" style="@if($ef->int_status==1)
                                                      background: green;
                                                      @elseif($ef->int_status==0)
                                                      background: red; @endif">{{ getDealName(true) }}
                                                   </div>
                                                </a>
                                                <div class="timeline-header">
                                                   <div>
                                                      <div class="user-img profileImage"
                                                         style=" background: #D36DDD;color: #fff;">
                                                         @if($ef->user)
                                                         {{ strtoupper( substr($ef->user->vchr_user_name ?? '', 0, 1))}}
                                                         @endif
                                                      </div>
                                                   </div>
                                                   <div class="timeline-heading">
                                                      <div class="col-md-9">
                                                         <a href="{{ ($ef->deleted_at == NULL)? url('user/deal-timeline').'/'.$ef->pk_int_deal_id : '#'}}" class="gotoDeal">
                                                            <p class="timeline-title">
                                                               <span class="user">{{ $ef->user->vchr_user_name??'' }}</span> added
                                                               a <span class="task">new {{ getDealName(true) }}</span> for <span class="assigne">{{$ef->agent->vchr_user_name??''}}</span>
                                                            </p>
                                                            <p class="m-0">Created on {{\App\Common\Variables::changeDateFormat($ef->created_at)}}
                                                               at {{\App\Common\Variables::changeTimeFormat($ef->created_at)}}
                                                            </p>
                                                            <div class="timeline-body">
                                                               <h6 class="title"></h6>
                                       
                                                               <p class="content-block">
                                                                  <span style="color: #080710 !important;font-weight:600">Title:&nbsp;</span>
                                                                  <span style="font-weight:500">{{$ef->deal_name}}</span>
                                                               </p>
                                                               <p class="content-block pt-1 pb-1">
                                                                  <span style="color: #080710 !important;font-weight:600">Amount:&nbsp;</span>
                                                                  <span style="font-weight:500">{!!$currency!!} {{$ef->deal_amount?$ef->deal_amount : '0'}}</span>
                                                               </p>
                                                               <p class="content-block">
                                                                  <span style="color: #080710 !important;font-weight:600">Assigned To:&nbsp;</span>
                                                                  <span style="font-weight:500">{{$ef->agent ? $ef->agent->vchr_user_name : 'UnAssigned'}}</span>
                                                               </p>
                                                               
                                                            </div>
                                                         </a>
                                                      </div>
                                                      <div class="col-md-3 pr-0">
                                                         <div class="timeline-action">
                                                            
                                                         </div>
                                                      </div>
                                                   </div>
                                                </div>
                                                <div class="task-schedule-date">
                                                   <p><img src="/backend/images/deals/calender.svg">Date:&nbsp;<span>
                                                      {{ $ef->start_date ? Carbon\Carbon::parse($ef->start_date)->format('d-m-Y') : ''}} To {{ $ef->end_date ? Carbon\Carbon::parse($ef->end_date)->format('d-m-Y') : 'No end date'}}
                                                      </span>
                                                   </p>
                                                   <p class="content-block">Status:&nbsp;
                                                      <span style="color: #34BFA3 !important">{{$ef->dealStage? $ef->dealStage->deal_stage_name : 'No Status'}}</span>
                                                   </p>
                                                   <p class="content-block">Stage:&nbsp;
                                                      <span style="color: #34BFA3 !important">{{$ef->dealStage? ($ef->dealStage->dealCategory? $ef->dealStage->dealCategory->name : 'No category' ) : 'No category'}}</span>
                                                   </p>
                                                   <p class="content-block">&nbsp;
                                                      <span style="color: #1728a8 !important">{{ $ef->deleted_at == NULL? 'Active '.getDealName(true) : 'Deleted '.getDealName(true) }}</span>
                                                   </p>
                                                </div>
                                             </div>
                                          </li>
                                          @else
                                          {{-- End deal listing --}}
                                          {{-- Visit details --}}
                                          @if($ef->visit_note && \Getlead\Rolespermissions\Models\RolePermission::hasPermission('timeline.view.visits'))
                                          <li class="timeline-inverted" id="time_line_{{$ef->id}}">
                                             <div class="timeline-badge tl-badge-bdr-drk-green">
                                                <i class="fa fa-whatsapp"></i>
                                             </div>
                                             <div class="timeline-panel">
                                                <div class="panel-title tl-panel-title-bg-drk-green">Visit</div>
                                                <div class="timeline-header tl-panel-hdr-lght-drk-green">
                                                   <div>
                                                      <div class="user-img profileImage" style=" background: #D36DDD;color: #fff;"></div>
                                                   </div>
                                                   <div class="timeline-heading">
                                                      <div class="col-md-9">
                                                         <p class="timeline-title">
                                                            <span class="task">Customer visited</span>
                                                         </p>
                                                         <p class="m-0">{{ \Carbon\Carbon::parse($ef->date_in)->format('M d')}}
                                                            at {{ \Carbon\Carbon::parse($ef->date_in)->format('g:i A ')}}
                                                         </p>
                                                         <div class="timeline-body">
                                                            <h6 class="title"></h6>
                                                            <p class="content-block">Assigned To
                                                               : {{$ef->agent->first() ? $ef->agent->first()->vchr_user_name : ""}}
                                                            </p>
                                                            <p class="content-block">Purpose
                                                               : {{$ef->purpose->first() ? $ef->purpose->first()->vchr_purpose : ""}}
                                                            </p>
                                                            <p class="content-block">Visit Note
                                                               : {{$ef->visit_note}}
                                                            </p>
                                                         </div>
                                                      </div>
                                                      <div class="col-md-3 pr-0 text-right">
                                                         <div class="timeline-action">
                                                         </div>
                                                      </div>
                                                   </div>
                                                </div>
                                             </div>
                                          </li>
                                          {{-- End visit details --}}
                                          <!----------------------Whatsapp History ------------------->
                                          @elseif($ef->log_type==\App\BackendModel\EnquiryFollowup::WHATSAPP_HISTORY && \Getlead\Rolespermissions\Models\RolePermission::hasPermission('timeline.view.whatsapp'))
                                          <li class="timeline-inverted" id="time_line_{{$ef->id}}">
                                             <div class="timeline-badge tl-badge-bdr-drk-green">
                                                <i class="fa fa-whatsapp"></i>
                                             </div>
                                             <div class="timeline-panel">
                                                <div class="panel-title tl-panel-title-bg-drk-green">Whatsapp</div>
                                                <div class="timeline-header tl-panel-hdr-lght-drk-green">
                                                   <div>
                                                      <div class="user-img profileImage" style=" background: #D36DDD;color: #fff;"></div>
                                                   </div>
                                                   <div class="timeline-heading">
                                                      <div class="col-md-9">
                                                         <p class="timeline-title">
                                                            <span class="user">{{ $ef->added_by ? $ef->added_by['vchr_user_name'] : ''}}</span> send
                                                            a <span class="task">whatsapp message</span>
                                                         </p>
                                                         <p class="m-0">{{ \Carbon\Carbon::parse($ef->created_at)->format('M d')}}
                                                            at {{ \Carbon\Carbon::parse($ef->created_at)->format('g:i A ')}}
                                                         </p>
                                                         <div class="timeline-body">
                                                            <p class="content-block">
                                                               {!!$ef->note!!}
                                                               @if($ef->custom_field_values?->count()>0)
                                                            <ul>
                                                               @foreach($ef->custom_field_values as $fieldvalue)
                                                               <li>
                                                                  <label><b>{{$fieldvalue->field ? $fieldvalue->field->field_caption : "NO NAME"}}
                                                                  :</b> @if($fieldvalue->field && substr($fieldvalue->field->field_type,0,4)=='file')
                                                                  <a href="{{route('user.downloadfile')}}?file_name={{$fieldvalue->field_value}}"
                                                                     target="_blank">View</a>@elseif($fieldvalue->field && $fieldvalue->field->field_type=='link')
                                                                  <a href="{{$fieldvalue->field_value}}"
                                                                     target="_blank">View</a>@else {{ $fieldvalue->field_value }} @endif
                                                                  </label>
                                                               </li>
                                                               @endforeach
                                                            </ul>
                                                            @endif
                                                            </p>
                                                         </div>
                                                      </div>
                                                      <div class="col-md-3 pr-0 text-right">
                                                         <div class="timeline-action">
                                                            
                                                         </div>
                                                      </div>
                                                   </div>
                                                </div>
                                             </div>
                                          </li>
                                          @elseif($ef->log_type==\App\BackendModel\EnquiryFollowup::IVR && \Getlead\Rolespermissions\Models\RolePermission::hasPermission('timeline.view.ivr'))
                                          <li class="timeline-inverted" id="time_line_{{$ef->id}}">
                                             <div class="timeline-badge tl-badge-bdr-drk-yellow"><i
                                                class="fa fa-phone-square" style="margin-top: 7px;color: white;"></i>
                                             </div>
                                             <div class="timeline-panel">
                                                <div class="panel-title tl-panel-title-bg-drk-yellow">IVR</div>
                                                <div class="timeline-header tl-panel-hdr-lght-drk-yellow">
                                                   <div>
                                                      <div class="user-img profileImage"
                                                         style=" background: #D36DDD;color: #fff;"></div>
                                                   </div>
                                                   <div class="timeline-heading">
                                                      <div class="col-md-9">
                                                         <p class="timeline-title"><strong class="pr-2">{{$ef->added_by ? $ef->added_by['vchr_user_name'] : ''}}</strong>New Calls Through IVR</p>
                                                         <p class="m-0">{{ \Carbon\Carbon::parse($ef->created_at)->format('M d')}}
                                                            at {{ \Carbon\Carbon::parse($ef->created_at)->format('g:i A ')}}
                                                         </p>
                                                         
                                                         @if($ef->ivrdata && $ef->ivrdata->vendor_id == \App\User::getVendorId() && ($ef->ivrdata->call_status=='Connected' || $ef->ivrdata->call_status=='ANSWERED'|| $ef->ivrdata->call_status=='ANSWER'))
                                                         <p class="m-0">
                                                         <div class="col-md-6">
                                                              
                                                            @if(strpos($ef?->ivrdata?->recording_url,"https://pbx.voxbaysolutions.com") !== false)
                                                            <audio controls="controls">
                                                                   <source src="{{ str_replace(" ","+",$ef->ivrdata->recording_url)}}"
                                                                  type="audio/wav"/>
                                                            </audio>
                                                            @else
                                                               <audio controls="controls">
                                                                  <source src="{{ strpos($ef->ivrdata->recording_url, 'https://') === 0 ? $ef->ivrdata->recording_url : 'https://pbx.voxbaysolutions.com/callrecordings/' . str_replace(" ","+",$ef->ivrdata->recording_url) }}"
                                                                  type="audio/wav"/>
                                                               </audio>
                                                            @endif
                                                         </div>
                                                         </p>
                                                         @elseif($ef->callMaster && $ef->callMaster->vendor_id == \App\User::getVendorId() && ($ef->callMaster->call_status == 'Connected' || $ef->callMaster->call_status=='ANSWERED'))
                                                         <p class="m-0">
                                                         <div class="col-md-6">
                                                            <audio controls="controls">
                                                               <source src="{{$ef->callMaster->recording_url}}"
                                                                  type="audio/wav"/>
                                                            </audio>
                                                         </div>
                                                         </p>
                                                         @endif
                                                         <div class="timeline-body">
                                                            <p class="content-block"> Ref DID: {!!$ef->note!!}
                                                               @if($ef->custom_field_values?->count()>0)
                                                            <ul>
                                                               @foreach($ef->custom_field_values as $fieldvalue)
                                                               <li>
                                                                  <label><b>{{$fieldvalue->field ? $fieldvalue->field->field_caption : "NO NAME"}}
                                                                  :</b> @if($fieldvalue->field && substr($fieldvalue->field->field_type,0,4)=='file')
                                                                  <a href="{{route('user.downloadfile')}}?file_name={{$fieldvalue->field_value}}"
                                                                     target="_blank">View</a>@elseif($fieldvalue->field && $fieldvalue->field->field_type=='link')
                                                                  <a href="{{$fieldvalue->field_value}}"
                                                                     target="_blank">View</a>@else {{ $fieldvalue->field_value }} @endif
                                                                  </label>
                                                               </li>
                                                               @endforeach
                                                            </ul>
                                                            @endif</p>
                                                         </div>
                                                      </div>
                                                      <div class="col-md-3 pr-0 text-right">
                                                         <div class="timeline-action">
                                                            
                                                         </div>
                                                      </div>
                                                   </div>
                                                </div>
                                             </div>
                                          </li>
                                          <!----------------------Sms History ------------------->
                                          @elseif($ef->log_type==\App\BackendModel\EnquiryFollowup::SMS_HISTORY && \Getlead\Rolespermissions\Models\RolePermission::hasPermission('timeline.view.calls'))
                                          <li class="timeline-inverted" id="time_line_{{$ef->id}}">
                                             <div class="timeline-badge tl-badge-bdr-yellow"><i
                                                class="fa fa-commenting-o" style="margin-top: 10px"></i>
                                             </div>
                                             <div class="timeline-panel">
                                                <div class="panel-title tl-panel-title-bg-yellow">SMS</div>
                                                <div class="timeline-header tl-panel-hdr-lght-cream">
                                                   <div>
                                                      <div class="user-img profileImage"
                                                         style=" background: #D36DDD;color: #fff;"></div>
                                                   </div>
                                                   <div class="timeline-heading">
                                                      <div class="col-md-9">
                                                         <p class="timeline-title">
                                                            <strong class="pr-2">{{$ef->added_by ? $ef->added_by['vchr_user_name'] : ''}}</strong>send
                                                            a sms
                                                         </p>
                                                         <p class="m-0">{{ \Carbon\Carbon::parse($ef->created_at)->format('M d')}}
                                                            at {{ \Carbon\Carbon::parse($ef->created_at)->format('g:i A ')}}
                                                         </p>
                                                         <div class="timeline-body">
                                                            @if(isset($smsHistorys))
                                                            @foreach($smsHistorys as $smsHistory)
                                                            @if($smsHistory->enquiry_followup_id==$ef->id)
                                                            <p class="content-block">{{$smsHistory->text_message }}</p>
                                                            <p class="content-block">
                                                               Status:{{$smsHistory->vchr_status }}
                                                            </p>
                                                            @endif
                                                            @endforeach
                                                            @endif
                                                         </div>
                                                      </div>
                                                      <div class="col-md-3 pr-0 text-right">
                                                         <div class="timeline-action">
                                                            
                                                         </div>
                                                      </div>
                                                   </div>
                                                </div>
                                             </div>
                                          </li>
                                          <!----------------------Order History ------------------->
                                          @elseif($ef->log_type==\App\BackendModel\EnquiryFollowup::TYPE_ORDER)
                                          @if(isset($tracking_data['tracking_data']))
                                          <li class="timeline-inverted" id="time_line_{{$ef->id}}">
                                             <div class="timeline-badge tl-badge-bdr-yellow">
                                                <i class="fa fa-shopping-cart" aria-hidden="true" style="margin-top: 10px;"></i>
                                             </div>
                                             <div class="timeline-panel">
                                                <div class="panel-title tl-panel-title-bg-yellow">ORDER DETAILS
                                                </div>
                                                <div class="timeline-header tl-panel-hdr-lght-cream">
                                                   <div>
                                                      <div class="user-img profileImage"
                                                         style=" background: #D36DDD;color: #fff;"></div>
                                                   </div>
                                                   <div class="timeline-heading">
                                                      <div class="col-md-9">
                                                         <p class="timeline-title">
                                                            <strong class="pr-2">Ordered with
                                                            shiprocket</strong>Order Id # {{ $ef->name }}
                                                         </p>
                                                         <p class="m-0">Ordered
                                                            at {{\App\Common\Variables::changeDateFormat($enquiry->purchase_date)}}
                                                            at {{\App\Common\Variables::changeTimeFormat($enquiry->purchase_date)}}
                                                         </p>
                                                         <div class="timeline-body">
                                                            <div class="accordion js-accordion">
                                                               <div class="accordion__item js-accordion-item">
                                                                  <div class="accordion-header js-accordion-header">
                                                                     Product Details
                                                                  </div>
                                                                  <div class="accordion-body js-accordion-body">
                                                                     <div class="accordion-body__contents">
                                                                        @php
                                                                        $products = json_decode($ef->note, true);
                                                                        @endphp
                                                                        <p>Product Name
                                                                           - {{ $products['product_name'] }}
                                                                        </p>
                                                                        <p>Quantity Ordered
                                                                           - {{ $products['product_quantity'] }}
                                                                        </p>
                                                                        <p>Product Cost
                                                                           - {{ $products['product_cost'] }}
                                                                        </p>
                                                                        <p>Total - {{ $products['total'] }}</p>
                                                                        <p>Tax - {{ $products['tax'] }}</p>
                                                                     </div>
                                                                  </div>
                                                                  <!-- end of accordion body -->
                                                               </div>
                                                               <!-- end of accordion item -->
                                                               <div class="accordion__item js-accordion-item ">
                                                                  <div class="accordion-header js-accordion-header">
                                                                     Shipment Details
                                                                  </div>
                                                                  <div class="accordion-body js-accordion-body">
                                                                     <div class="accordion-body__contents">
                                                                        <p>Billing name
                                                                           - {{ $products['billing_name'] }}
                                                                        <p>
                                                                        <p>Billing email
                                                                           - {{ $products['billing_email'] }}
                                                                        <p>
                                                                        <p>Billing mobile
                                                                           - {{ $products['billing_phone'] }}
                                                                        <p>
                                                                        <p>Billing address
                                                                           - {{ $products['billing_address'] }}
                                                                        <p>
                                                                        <p>Billing state
                                                                           - {{ $products['billing_state'] }}
                                                                        <p>
                                                                        <p>Billing country
                                                                           - {{ $products['billing_country'] }}
                                                                        <p>
                                                                        <p>Billing Pincode
                                                                           - {{ $products['billing_pincode'] }}
                                                                        <p>
                                                                     </div>
                                                                  </div>
                                                                  <!-- end of accordion body -->
                                                               </div>
                                                               <div class="accordion__item js-accordion-item">
                                                                  <div class="accordion-header js-accordion-header">
                                                                     Billing Details
                                                                  </div>
                                                                  <div class="accordion-body js-accordion-body">
                                                                     <div class="accordion-body__contents">
                                                                        <p>Billing name
                                                                           - {{ $products['billing_name'] }}
                                                                        <p>
                                                                        <p>Billing email
                                                                           - {{ $products['billing_email'] }}
                                                                        <p>
                                                                        <p>Billing mobile
                                                                           - {{ $products['billing_phone'] }}
                                                                        <p>
                                                                        <p>Billing address
                                                                           - {{ $products['billing_address'] }}
                                                                        <p>
                                                                        <p>Billing state
                                                                           - {{ $products['billing_state'] }}
                                                                        <p>
                                                                        <p>Billing country
                                                                           - {{ $products['billing_country'] }}
                                                                        <p>
                                                                        <p>Billing Pincode
                                                                           - {{ $products['billing_pincode'] }}
                                                                        <p>
                                                                     </div>
                                                                  </div>
                                                                  <!-- end of accordion body -->
                                                               </div>
                                                               @if(isset($tracking_data['tracking_data']))
                                                               <div class="accordion__item js-accordion-item ">
                                                                  <div class="accordion-header js-accordion-header">
                                                                     Tracking Details
                                                                  </div>
                                                                  <div class="accordion-body js-accordion-body">
                                                                     <div class="accordion-body__contents">
                                                                        <div class="tracking">
                                                                           <div class="card tracking-scroll">
                                                                              <div class="px-3 top text-right">
                                                                                 <div>
                                                                                    <h5 style="text-align:right;">
                                                                                       ORDER ID <span
                                                                                          class="text-primary font-weight-bold">#</span>{{ $ef->name }}
                                                                                    </h5>
                                                                                 </div>
                                                                                 <div class="d-flex flex-column text-sm-right">
                                                                                    @if($tracking_data['tracking_data']['shipment_status'] == 7)
                                                                                    <p class="mb-0"
                                                                                       style="font-size: 15px;">
                                                                                       Item delivered<span>&nbsp;at {{\App\Common\Variables::changeDateFormat($tracking_data['tracking_data']['shipment_track'][0]['delivered_date'])}}&nbsp;,{{\App\Common\Variables::changeTimeFormat($tracking_data['tracking_data']['shipment_track'][0]['delivered_date'])}}</span>
                                                                                    </p>
                                                                                    @elseif($tracking_data['tracking_data']['shipment_status'] == 6)
                                                                                    <p class="mb-0"
                                                                                       style="font-size: 12px;">
                                                                                       Item shipped<span>&nbsp;with shipment ID # {{ $tracking_data['tracking_data']['shipment_track'][0]['id'] }}</span>
                                                                                    </p>
                                                                                    @elseif($tracking_data['tracking_data']['shipment_status'] == 19)
                                                                                    <p class="mb-0"
                                                                                       style="font-size: 12px;">
                                                                                       Ready for
                                                                                       pickup<span></span>
                                                                                    </p>
                                                                                    @else
                                                                                    <p class="mb-0"
                                                                                       style="font-size: 15px;">
                                                                                       Order
                                                                                       processing<span></span>
                                                                                    </p>
                                                                                    @endif
                                                                                    <p class="mb-0"><a
                                                                                       target="_blank"
                                                                                       href="{{ $tracking_data['tracking_data']['track_url'] }}">{{ $tracking_data['tracking_data']['track_url'] }}</a>
                                                                                    </p>
                                                                                 </div>
                                                                              </div>
                                                                              <!-- Add class 'active' to progress -->
                                                                              <div class="ul-scroll">
                                                                                 <ul class="" style="margin: 20px;">
                                                                                    @foreach($tracking_data['tracking_data']['shipment_track_activities'] as $key => $tracking)
                                                                                    <li style="padding: 6px 0px;border-bottom: 1px dashed #ccc;font-size: 10px;">{{ $tracking['activity'] }}
                                                                                       at {{\App\Common\Variables::changeDateFormat($tracking['date'])}}
                                                                                       &nbsp;,{{\App\Common\Variables::changeTimeFormat($tracking['date'])}}
                                                                                    </li>
                                                                                    @endforeach
                                                                                 </ul>
                                                                              </div>
                                                                           </div>
                                                                        </div>
                                                                     </div>
                                                                  </div>
                                                                  <!-- end of accordion body -->
                                                               </div>
                                                               @endif
                                                            </div>
                                                         </div>
                                                      </div>
                                                      <div class="col-md-3 pr-0">
                                                         <div class="timeline-action">
                                                            
                                                         </div>
                                                      </div>
                                                   </div>
                                                </div>
                                             </div>
                                          </li>
                                          @else
                                          <li class="timeline-inverted" id="time_line_{{$ef->id}}">
                                             <!-- <div class="timeline-badge tl-badge-bdr-drk-green"><i class="fa fa-edit"
                                                style="margin-top: 10px"></i>
                                                </div> -->
                                             <div class="timeline-panel">
                                                <div class="panel-title tl-panel-title-bg-drk-green">
                                                   Order
                                                </div>
                                                <div class="timeline-header tl-panel-hdr-lght-drk-green">
                                                   <div>
                                                      <div class="user-img profileImage" style=" background: #D36DDD;color: #fff;"></div>
                                                   </div>
                                                   <div class="timeline-heading">
                                                      <div class="col-md-9">
                                                         <p class="timeline-title">
                                                            <span class="user">{{$ef->added_by ? $ef->added_by['vchr_user_name'] : ''}}</span> placed an <span class="task">order</span>
                                                         <p class="m-0">{{ \Carbon\Carbon::parse($ef->created_at)->format('M d')}}
                                                            at {{ \Carbon\Carbon::parse($ef->created_at)->format('g:i A ')}}
                                                         </p>
                                                         <div class="timeline-body">
                                                            <p class="content-block">
                                                            </p>
                                                         </div>
                                                      </div>
                                                      <div class="col-md-3 pr-0 text-right">
                                                         <div class="timeline-action">
                                                            
                                                         </div>
                                                      </div>
                                                   </div>
                                                </div>
                                             </div>
                                          </li>
                                          @endif
                                          
                                          <!------ campign task ------>
                                          @elseif($ef->log_type==\App\BackendModel\EnquiryFollowup::TASK_COMPLETED)
                                          <li class="timeline-inverted" id="time_line_{{$ef->id}}">
                                             <div class="timeline-badge tl-badge-bdr-fb-blue">
                                                <i class="fa fa-sticky-note-o" style="margin-top: 10px"></i>
                                             </div>
                                             <div class="timeline-panel">
                                                <div class="panel-title tl-panel-title-bg-fb-blue">Status</div>
                                                <div class="timeline-header tl-panel-hdr-lght-fb-blue">
                                                   <div>
                                                   
                                                      <div class="profileImage user-img" style=" background: #D36DDD;color: #fff;">
                                                         {{ (isset($ef->added_by['vchr_user_name']))? strtoupper(  substr($ef->added_by['vchr_user_name'], 0, 1)) : ''}}
                                                      </div>
                                                   </div>
                                                   <div class="timeline-heading">
                                                      <div class="col-md-9">
                                                         <p class="timeline-title">
                                                            <span class="user">{{ (isset($ef->added_by['vchr_user_name']))? $ef->added_by['vchr_user_name'] : ''}}</span> Completed
                                                            the <span class="task">@if($ef?->task?->campaign) Campaign @endif Task</span>
                                                         </p>
                                                         <p class="m-0">{{ \Carbon\Carbon::parse($ef->created_at)->format('M d')}}
                                                            at {{ \Carbon\Carbon::parse($ef->created_at)->format('g:i A ')}}
                                                         </p>
                                                         <div class="timeline-body">
                                                            <p>{{$ef->task?($ef?->task?->campaign?$ef?->task?->campaign->name:$ef?->task->name):''}}</p>
                                                           {{-- <p> {!!$ef?->currentStatus?->vchr_status!!}</p> --}}
                                                         
                                                         </div>
                                                      </div>
                                                      <div class="col-md-3 pr-0 text-right">
                                                     
                                                      </div>
                                                   </div>
                                                </div>
                                             </div>
                                          </li>
                                          <!-- -- india mart leads  -->
                                          @elseif($ef->log_type==\App\BackendModel\EnquiryFollowup::TYPE_INDIA_MART)
                                          <li class="timeline-inverted" id="time_line_{{$ef->id}}">
                                             <div style="border: 2px solid #f68692 !important;"
                                                class="timeline-badge tl-badge-bdr-yellow"><i
                                                class="fa fa-info-circle" aria-hidden="true"
                                                style="margin-top: 10px;color: #f68692 !important;"></i>
                                             </div>
                                             <div class="timeline-panel">
                                                <div style="background: #ff8692 !important;"
                                                   class="panel-title tl-panel-title-bg-yellow">Lead Details
                                                </div>
                                                <div class="timeline-header tl-panel-hdr-lght-cream">
                                                   <div>
                                                      <div class="user-img profileImage"
                                                         style=" background: #D36DDD;color: #fff;"></div>
                                                   </div>
                                                   <div class="timeline-heading">
                                                      <div class="col-md-9">
                                                         <p class="timeline-title">
                                                            <strong class="pr-2">Lead from indiamart
                                                            with</strong>Enquiry Id # {{ $ef->name }}
                                                         </p>
                                                         @php
                                                         $leadData = json_decode($ef->note, true);
                                                         @endphp
                                                         <p class="m-0">Created
                                                            at {{\App\Common\Variables::changeDateFormat($leadData['created_at'])}}
                                                            at {{\App\Common\Variables::changeTimeFormat($leadData['created_at'])}}
                                                         </p>
                                                         <div class="timeline-body">
                                                            <div class="accordion js-accordion">
                                                               <div class="accordion__item js-accordion-item">
                                                                  <div class="accordion-header js-accordion-header">
                                                                     Details
                                                                  </div>
                                                                  <div class="accordion-body js-accordion-body">
                                                                     <div class="accordion-body__contents">
                                                                        @php
                                                                        $address = json_decode($enquiry->address, true);
                                                                        @endphp
                                                                        <p>Subject - {{ $leadData['subject'] }}</p>
                                                                        <p>Company name
                                                                           - @if($leadData['company_name']) {{ $leadData['company_name'] }} @else
                                                                           Not specified @endif
                                                                        </p>
                                                                        <p>Message - {{ $leadData['message'] }}</p>
                                                                        <p>Product Name
                                                                           - {{ $leadData['product_name'] }}
                                                                        </p>
                                                                        <p>Address
                                                                           - {{ $address? $address['customer_address']:''}}
                                                                        </p>
                                                                     </div>
                                                                  </div>
                                                                  <!-- end of accordion body -->
                                                               </div>
                                                               <!-- end of accordion item -->
                                                            </div>
                                                         </div>
                                                      </div>
                                                      <div class="col-md-3">
                                                         <div class="timeline-action">
                                                            
                                                         </div>
                                                      </div>
                                                   </div>
                                                </div>
                                             </div>
                                          </li>
                                          <!----------------------Status ------------------->
                                          @elseif($ef->log_type==\App\BackendModel\EnquiryFollowup::TYPE_STATUS && \Getlead\Rolespermissions\Models\RolePermission::hasPermission('timeline.view.lead_updates'))
                                          @include('backend.user-pages.timeline.status.enquiry-status')
                                          <!----------------------Note ------------------->
                                          @elseif($ef->log_type==\App\BackendModel\EnquiryFollowup::TYPE_NOTE && \Getlead\Rolespermissions\Models\RolePermission::hasPermission('timeline.view.notes'))
                                          @include('backend.user-pages.timeline.status.enquiry-log-note')
                                          @elseif($ef->log_type==\App\BackendModel\EnquiryFollowup::TYPE_VOICE_NOTE && \Getlead\Rolespermissions\Models\RolePermission::hasPermission('timeline.view.voice_notes'))
                                          <li class="timeline-inverted" id="time_line_{{$ef->id}}">
                                             <div class="timeline-badge tl-badge-bdr-blue">
                                                <i class="fa fa-file-text" style="margin-top: 10px"></i>
                                             </div>
                                             <div class="timeline-panel">
                                                <div class="panel-title tl-panel-title-bg-blue">Voice Note</div>
                                                <div class="timeline-header tl-panel-hdr-lght-blue">
                                                   <div>
                                                      <div class="user-img profileImage"
                                                         style=" background: #D36DDD;color: #fff;">
                                                         {{ ($ef->added_by)? strtoupper(  substr($ef->added_by['vchr_user_name'], 0, 1)) : ''}}
                                                      </div>
                                                   </div>
                                                   <div class="timeline-heading">
                                                      <div class="col-md-9">
                                                         <p class="timeline-title"><strong class="pr-2">{{$ef->added_by ? $ef->added_by['vchr_user_name'] : ''}} </strong>
                                                            left a voice note
                                                         </p>
                                                         <p class="m-0">{{\App\Common\Variables::changeDateFormat($ef->created_at)}}
                                                            at {{\App\Common\Variables::changeTimeFormat($ef->created_at)}}
                                                         </p>
                                                         <p class="m-0">
                                                         <div class="col-md-6 pt-4">
                                                            <audio controls="controls">
                                                               <source src="{{ Storage::disk('s3')->url($ef->note) }}"
                                                                  type="audio/wav"/>
                                                            </audio>
                                                         </div>
                                                         
                                                         </p>
                                                      </div>
                                                      <div class="col-md-3 pr-0 text-right">
                                                         <div class="timeline-action">
                                                            
                                                         </div>
                                                      </div>
                                                   </div>
                                                </div>
                                             </div>
                                          </li>
                                          <!----------------------Log------------------->
                                          @elseif($ef->log_type==\App\BackendModel\EnquiryFollowup::TYPE_FILE_NOTE)
                                          <li class="timeline-inverted" id="time_line_{{$ef->id}}">
                                             <div class="timeline-badge tl-badge-bdr-blue">
                                                <i class="fa fa-file-text" style="margin-top: 10px"></i>
                                             </div>
                                             <div class="timeline-panel">
                                                <div class="panel-title tl-panel-title-bg-blue">File Note</div>
                                                <div class="timeline-header tl-panel-hdr-lght-blue">
                                                   <div>
                                                      <div class="user-img profileImage"
                                                         style=" background: #D36DDD;color: #fff;">
                                                         {{ ($ef->added_by)? strtoupper(  substr($ef->added_by['vchr_user_name'], 0, 1)) : ''}}
                                                      </div>
                                                   </div>
                                                   <div class="timeline-heading">
                                                      <div class="col-md-9">
                                                         <p class="timeline-title">
                                                            <span class="user">{{$ef->added_by ? $ef->added_by['vchr_user_name'] : ''}} </span>
                                                            left a <span class="task">file note</span>
                                                         </p>
                                                         <p class="m-0">{{\App\Common\Variables::changeDateFormat($ef->created_at)}}
                                                            at {{\App\Common\Variables::changeTimeFormat($ef->created_at)}} 
                                                         </p>
                                                         <p class="m-0">
                                                         <div class="col-md-6 pt-4">
                                                            <a href="{{ Storage::disk('s3')->url($ef->note) }}"
                                                               target="_blank" class="btn btn-secondary">View File
                                                            </a>
                                                         </div>
                                                         </p>
                                                      </div>
                                                      <div class="col-md-3 pr-0 text-right">
                                                         <div class="timeline-action">
                                                            
                                                         </div>
                                                      </div>
                                                   </div>
                                                </div>
                                             </div>
                                          </li>
                                          @elseif($ef->log_type==\App\BackendModel\EnquiryFollowup::TYPE_DOCUMENT)
                                          <li class="timeline-inverted" id="time_line_{{$ef->id}}">
                                             <div class="timeline-badge tl-badge-bdr-blue">
                                                <i class="fa fa-file-text" style="margin-top: 10px"></i>
                                             </div>
                                             <div class="timeline-panel">
                                                <div class="panel-title tl-panel-title-bg-blue">Document Note</div>
                                                <div class="timeline-header tl-panel-hdr-lght-blue">
                                                   <div>
                                                      <div class="user-img profileImage"
                                                         style=" background: #D36DDD;color: #fff;">
                                                         {{ ($ef->added_by)? strtoupper(  substr($ef->added_by['vchr_user_name'], 0, 1)) : ''}}
                                                      </div>
                                                   </div>
                                                   <div class="timeline-heading">
                                                      <div class="col-md-9">
                                                         <p class="timeline-title">
                                                            <span class="user">{{$ef->added_by ? $ef->added_by['vchr_user_name'] : ''}} </span>
                                                            left a <span class="task">document note</span>
                                                         </p>
                                                         <p class="m-0">{{\App\Common\Variables::changeDateFormat($ef->created_at)}}
                                                            at {{\App\Common\Variables::changeTimeFormat($ef->created_at)}} 
                                                         </p>
                                                         <p class="m-0">
                                                         <div class="col-md-6 pt-4">
                                                            <a href="{{ Storage::disk('s3')->url($ef->note) }}"
                                                               target="_blank" class="btn btn-secondary">View File
                                                            </a>
                                                         </div>
                                                         </p>
                                                      </div>
                                                      <div class="col-md-3 pr-0 text-right">
                                                         <div class="timeline-action">
                                                         
                                                         </div>
                                                      </div>
                                                   </div>
                                                </div>
                                             </div>
                                          </li>
                                          <!----------------------Log------------------->
                                          @elseif($ef->log_type==\App\BackendModel\EnquiryFollowup::TYPE_LOG_CALL)
                                          @include('backend.user-pages.timeline.status.enquiry-log-call')
                                          <!----------------------  Email ----------------->
                                          @elseif($ef->log_type==\App\BackendModel\EnquiryFollowup::TYPE_LOG_EMAIL && \Getlead\Rolespermissions\Models\RolePermission::hasPermission('timeline.view.emails'))
                                          @include('backend.user-pages.timeline.status.enquiry-log-email')
                                          @elseif($ef->log_type==\App\BackendModel\EnquiryFollowup::TYPE_LOG_MEETING)
                                          @include('backend.user-pages.timeline.status.enquiry-log-meeting')
                                          <!----------------------  Schedule ----------------->
                                          <!----------------------Task------------------>
                                          @elseif($ef->log_type==\App\BackendModel\EnquiryFollowup::TYPE_TASK)
                                          <li class="timeline-inverted" id="time_line_{{$ef->id}}">
                                             <div class="timeline-badge tl-badge-bdr-red"><i class="fa fa-file-o"
                                                style="margin-top: 10px"></i>
                                             </div>
                                             <div class="timeline-panel pb-0">
                                                <div class="panel-title tl-panel-title-bg-red">Task</div>
                                                <div class="timeline-header tl-panel-hdr-lght-red">
                                                   <div>
                                                      <div class="user-img profileImage"
                                                         style=" background: #D36DDD;color: #fff;">
                                                          @if(isset($ef->assigned))
                                                         {{ strtoupper(  substr($ef->assigned['vchr_user_name'], 0, 1))}}
                                                         @else
                                                         {{ ($ef->added_by)? strtoupper(  substr($ef->added_by['vchr_user_name'], 0, 1)) : ''}}
                                                         @endif
                                                      </div>
                                                   </div>
                                                   <div class="timeline-heading">
                                                      <div class="col-md-9">
                                                         <p class="timeline-title">
                                                             @if(isset($ef->assigned))
                                                            <span class="user">{{$ef->assigned['vchr_user_name']}}</span>
                                                            had a <span class="task">new task</span>
                                                         </p>
                                                         @else
                                                         <span class="user">{{$ef->added_by ? $ef->added_by['vchr_user_name'] : ''}}</span>
                                                         createda <span class="task">new task</span></p>
                                                         @endif
                                                         <p class="m-0">{{ \Carbon\Carbon::parse($ef->created_at)->format('M d')}}
                                                            at {{ \Carbon\Carbon::parse($ef->created_at)->format('g:i A ')}}
                                                         </p>
                                                         <div class="timeline-body p-0">
                                                            <div class="row m-0">
                                                               <div class="col-lg-3 bg-light-g p-0">
                                                                  <div class="tl-body-content-1">
                                                                     <ul>
                                                                        <li>
                                                                           <label>Task</label>
                                                                           <p>{{$ef->name}}</p>
                                                                        </li>
                                                                        <li class="bdr-top">
                                                                           <label>Type</label>
                                                                           <p>{{$ef->task_type}}</p>
                                                                        </li>
                                                                        <li class="bdr-top">
                                                                           <label>Email Reminder</label>
                                                                           @if($ef->reminder!=-1)
                                                                           <p>{{ \Carbon\Carbon::parse($ef->date)->subDays($ef->reminder)->format('M d')}}
                                                                              <span class="clr-grey light"> at </span>{{ \Carbon\Carbon::parse($ef->date)->format('g:i A ')}}
                                                                           </p>
                                                                           @else
                                                                           <p>No Reminder</p>
                                                                           @endif
                                                                        </li>
                                                                        <li class="">
                                                                           <label>Email Remainder</label>
                                                                           @if($ef->reminder==0)
                                                                           <p>No Remainder</p>
                                                                           @elseif($ef->reminder==1)
                                                                           <p>The day of</p>
                                                                           @elseif($ef->reminder==2)
                                                                           <p>The day before</p>
                                                                           @elseif($ef->reminder==8)
                                                                           <p>The week before</p>
                                                                           @endif
                                                                        </li>
                                                                     </ul>
                                                                  </div>
                                                               </div>
                                                               <div class="col-lg-9">
                                                                  <div class="tl-body-content-2">
                                                                     <p class="content-block">{!!$ef->note!!}
                                                                        @if($ef->custom_field_values?->count()>0)
                                                                     <ul>
                                                                        @foreach($ef->custom_field_values as $fieldvalue)
                                                                        <li>
                                                                           <label><b>{{$fieldvalue->field ? $fieldvalue->field->field_caption : "NO NAME"}}
                                                                           :</b> @if($fieldvalue->field && substr($fieldvalue->field->field_type,0,4)=='file')
                                                                           <a href="{{route('user.downloadfile')}}?file_name={{$fieldvalue->field_value}}"
                                                                              target="_blank">View</a>@elseif($fieldvalue->field && $fieldvalue->field->field_type=='link')
                                                                           <a href="{{$fieldvalue->field_value}}"
                                                                              target="_blank">View</a>@else {{ $fieldvalue->field_value }} @endif
                                                                           </label>
                                                                        </li>
                                                                        @endforeach
                                                                     </ul>
                                                                     @endif</p>
                                                                     <div class="btm-details">
                                                                        <div class="crtd-by">
                                                                           <label> Created By </label>
                                                                           <p> {{$ef->added_by ? $ef->added_by['vchr_user_name'] : ''}}</p>
                                                                        </div>
                                                                        <div class="due-date">
                                                                           <label> Due date</label>
                                                                           <p class="clr-red"> {{ \Carbon\Carbon::parse($ef->date)->format('M d')}}
                                                                              <span class="clr-grey light"> at </span>{{ \Carbon\Carbon::parse($ef->date)->format('g:i a ')}}
                                                                           </p>
                                                                        </div>
                                                                     </div>
                                                                  </div>
                                                               </div>
                                                            </div>
                                                         </div>
                                                      </div>
                                                      <div class="col-md-3 pr-0 text-right">
                                                         <div class="timeline-action">
                                                            
                                                         </div>
                                                      </div>
                                                   </div>
                                                </div>
                                             </div>
                                          </li>
                                          <!----------------------  Schedule ----------------->
                                          @elseif($ef->log_type==\App\BackendModel\EnquiryFollowup::TYPE_SCHEDULE)
                                          <li class="timeline-inverted" id="time_line_{{$ef->id}}">
                                             <div class="timeline-badge tl-badge-bdr-pink"><i class="fa fa-calendar"
                                                style="margin-top: 10px"></i>
                                             </div>
                                             <div class="timeline-panel pb-0">
                                                <div class="panel-title tl-panel-title-bg-pink">Schedule</div>
                                                <div class="timeline-header tl-panel-hdr-lght-pink">
                                                   <div>
                                                      <div class="user-img profileImage"
                                                         style=" background: #D36DDD;color: #fff;">
                                                         {{ ($ef->added_by)? strtoupper(  substr($ef->added_by['vchr_user_name'], 0, 1)) : ''}}
                                                      </div>
                                                   </div>
                                                   <div class="timeline-heading">
                                                      <div class="col-md-9">
                                                         <p class="timeline-title">
                                                            <span class="user">{{$ef->added_by ? $ef->added_by['vchr_user_name'] : ''}}</span>created
                                                            a new <span class="task">schedule</span>
                                                         </p>
                                                         <p class="m-0">{{\App\Common\Variables::changeDateFormat($ef->created_at)}}
                                                            at {{\App\Common\Variables::changeTimeFormat($ef->created_at)}}
                                                         </p>
                                                         <div class="timeline-body p-0">
                                                            <div class="row m-0">
                                                               <div class="col-lg-3 bg-light-g p-0">
                                                                  <div class="tl-body-content-1">
                                                                     <ul>
                                                                        <li>
                                                                           <label>Meeting For</label>
                                                                           <p> {{ $ef->name }}</p>
                                                                        </li>
                                                                        <li class="bdr-top">
                                                                           <label>Start Time</label>
                                                                           <p> {{ \Carbon\Carbon::parse($ef->date)->format('M d')}}
                                                                              <span class="clr-grey light"> </span>@if(\Carbon\Carbon::parse($ef->date)->format('g:i A') == '12:00 AM')   @else
                                                                              at  {{ \Carbon\Carbon::parse($ef->date)->format('g:i A')}} @endif
                                                                           </p>
                                                                        </li>
                                                                        @if($ef->duration)
                                                                        <li class="bdr-top">
                                                                           <label>Duration</label>
                                                                           <p>{{$ef->duration}}</p>
                                                                        </li>
                                                                        @endif
                                                                        @if($enquiry->followup_required==1)
                                                                        <li class="bdr-top">
                                                                           <label>Follow up Required</label>
                                                                           <p>Yes</p>
                                                                        </li>
                                                                        @endif
                                                                     </ul>
                                                                  </div>
                                                               </div>
                                                               <div class="col-lg-9">
                                                                  <div class="tl-body-content-2">
                                                                     <p class="content-block"> {!!$ef->note!!}
                                                                        @if($ef->custom_field_values?->count()>0)
                                                                     <ul>
                                                                        @foreach($ef->custom_field_values as $fieldvalue)
                                                                        <li>
                                                                           <label><b>{{$fieldvalue->field ? $fieldvalue->field->field_caption : "NO NAME"}}
                                                                           :</b> @if($fieldvalue->field && substr($fieldvalue->field->field_type,0,4)=='file')
                                                                           <a href="{{route('user.downloadfile')}}?file_name={{$fieldvalue->field_value}}"
                                                                              target="_blank">View</a>@elseif($fieldvalue->field && $fieldvalue->field->field_type=='link')
                                                                           <a href="{{$fieldvalue->field_value}}"
                                                                              target="_blank">View</a>@else {{ $fieldvalue->field_value }} @endif
                                                                           </label>
                                                                        </li>
                                                                        @endforeach
                                                                     </ul>
                                                                     @endif</p>
                                                                     <div class="btm-details">
                                                                        <div class="crtd-by">
                                                                           <label> Created By </label>
                                                                           <p> {{$ef->added_by ? $ef->added_by['vchr_user_name'] : ''}}</p>
                                                                        </div>
                                                                     </div>
                                                                  </div>
                                                               </div>
                                                            </div>
                                                         </div>
                                                      </div>
                                                      <div class="col-md-3 pr-0 text-right">
                                                         <div class="timeline-action">
                                                           
                                                         </div>
                                                      </div>
                                                   </div>
                                                </div>
                                             </div>
                                          </li>
                                          <!----------------------  Send Email ----------------->
                                          @elseif($ef->log_type==\App\BackendModel\EnquiryFollowup::TYPE_EMAIL && \Getlead\Rolespermissions\Models\RolePermission::hasPermission('timeline.view.emails'))
                                          <li class="timeline-inverted" id="time_line_{{$ef->id}}">
                                             <div class="timeline-badge tl-badge-bdr-cream"><i
                                                class="fa fa-envelope-o" style="margin-top: 10px"></i></div>
                                             <div class="timeline-panel pb-0">
                                                <div class="panel-title tl-panel-title-bg-cream">Email</div>
                                                <div class="timeline-header tl-panel-hdr-lght-cream">
                                                   <div>
                                                      <div class="user-img profileImage"
                                                         style=" background: #D36DDD;color: #fff;"> {{ ($ef->added_by)? strtoupper(  substr($ef->added_by['vchr_user_name'], 0, 1)) : ''}}</div>
                                                   </div>
                                                   <div class="timeline-heading">
                                                      <div class="col-md-9">
                                                         <p class="timeline-title">
                                                            <span class="user">{{$ef->added_by ? $ef->added_by['vchr_user_name'] : ''}}</span> send
                                                            an <span class="task">email</span>
                                                         </p>
                                                         <p class="m-0">{{ \Carbon\Carbon::parse($ef->created_at)->format('M d')}}
                                                            at {{ \Carbon\Carbon::parse($ef->created_at)->format('g:i A ')}}
                                                         </p>
                                                         <div class="timeline-body p-0">
                                                            <div class="row m-0">
                                                               <div class="col-lg-3 bg-light-g p-0">
                                                                  <div class="tl-body-content-1">
                                                                     <ul>
                                                                        <li>
                                                                           <label>Sent Date</label>
                                                                           <p> {{ \Carbon\Carbon::parse($ef->created_at)->format('M d')}}
                                                                           </p>
                                                                        </li>
                                                                        <li class="bdr-top">
                                                                           <label>Time</label>
                                                                           <p>{{ \Carbon\Carbon::parse($ef->created_at)->format('g:i A ')}}</p>
                                                                        </li>
                                                                     </ul>
                                                                  </div>
                                                               </div>
                                                               <div class="col-lg-9">
                                                                  <div class="tl-body-content-2">
                                                                     <div class="email">
                                                                        <label> Subject </label>
                                                                        <p class="medium"> {{$ef->task_type}} </p>
                                                                     </div>
                                                                     <div class="email">
                                                                        <label> CC </label>
                                                                        <p class="medium"> {{$ef->response}} </p>
                                                                     </div>
                                                                     <div class="email">
                                                                        <label> BCC </label>
                                                                        <p class="medium"> {{$ef->bcc}} </p>
                                                                     </div>
                                                                     @if($ef->name!=null)
                                                                     <p class="content-block"><a
                                                                        href="{{ Storage::disk('s3')->url('uploads/email/' . $ef->name) }}">View
                                                                        Attachment</a>
                                                                     </p>
                                                                     @endif
                                                                     <div class="">
                                                                        {!!$ef->note !!}
                                                                     </div>
                                                                     {{-- <div class="">
                                                                        <div class="crtd-by">
                                                                           <label> Sent By </label>
                                                                           <p> {{$ef->added_by ? $ef->added_by['vchr_user_name'] : ''}}</p>
                                                                        </div>
                                                                     </div> --}}
                                                                    
                                                                     
                                                                  </div>
                                                               </div>
                                                            </div>
                                                         </div>
                                                      </div>
                                                      <div class="col-md-3 pr-0 text-right">
                                                         <div class="timeline-action">
                                                            
                                                         </div>
                                                      </div>
                                                   </div>
                                                </div>
                                             </div>
                                          </li>
                                          <!----------------------Check in ------------------->
                                          @elseif($ef->log_type==\App\BackendModel\EnquiryFollowup::TYPE_CHECKIN)
                                          <li class="timeline-inverted" id="time_line_{{$ef->id}}">
                                             <div class="timeline-badge tl-badge-bdr-fb-blue">
                                                <i class="fa fa-sticky-note-o" style="margin-top: 10px"></i>
                                             </div>
                                             <div class="timeline-panel">
                                                <div class="panel-title tl-panel-title-bg-fb-blue">Check-in</div>
                                                <div class="timeline-header tl-panel-hdr-lght-fb-blue">
                                                   <div>
                                                      <div class="profileImage user-img"
                                                         style=" background: #6167e6;color: #fff;">
                                                         {{ ($ef->added_by)? strtoupper(  substr($ef->added_by['vchr_user_name'], 0, 1)) : ''}}
                                                      </div>
                                                   </div>
                                                   <div class="timeline-heading">
                                                      <div class="col-md-9">
                                                         <p class="timeline-title">
                                                            <span class="pr-2">{{$ef->added_by ? $ef->added_by['vchr_user_name'] : ''}}</span> checked in
                                                         </p>
                                                         <p class="m-0">{{ \Carbon\Carbon::parse($ef->created_at)->format('M d')}}
                                                            at {{ \Carbon\Carbon::parse($ef->created_at)->format('g:i A ')}}
                                                         </p>
                                                         <div class="timeline-body">
                                                            @if($ef->location)
                                                            @if($ef->location->latitude && $ef->location->longitude)
                                                              <p>  <a href="https://maps.google.com?q='{{$ef->location->latitude}},{{$ef->location->longitude}}'" target="blank" class="btn btn-bg-info"><i class="fa fa-map-marker" aria-hidden="true"></i></a> </p>
                                                            @endif
                                                            @endif
                                                       
                                                         </div>
                                                      </div>
                                                      <div class="col-md-3 pr-0">
                                                         <div class="timeline-action">
                                                         
                                                         </div>
                                                      </div>
                                                   </div>
                                                </div>
                                             </div>
                                          </li>
                                          <!----------------------Check in ------------------->                                        
                                          <!----------------------Checkout ------------------->
                                          @elseif($ef->log_type==\App\BackendModel\EnquiryFollowup::TYPE_CHECKOUT)
                                         
                                          <li class="timeline-inverted" id="time_line_{{$ef->id}}">
                                             <div class="timeline-badge tl-badge-bdr-fb-blue">
                                                <i class="fa fa-sticky-note-o" style="margin-top: 10px"></i>
                                             </div>
                                             <div class="timeline-panel">
                                                <div class="panel-title tl-panel-title-bg-fb-blue">Check-out</div>
                                                <div class="timeline-header tl-panel-hdr-lght-fb-blue">
                                                   <div>
                                                      <div class="profileImage user-img"
                                                         style=" background: #6167e6;color: #fff;">
                                                         {{ ($ef->added_by)? strtoupper(  substr($ef->added_by['vchr_user_name'], 0, 1)) : ''}}
                                                      </div>
                                                   </div>
                                                   <div class="timeline-heading">
                                                      <div class="col-md-9">
                                                         <p class="timeline-title">
                                                            <span class="user">{{$ef->added_by ? $ef->added_by['vchr_user_name'] : ''}}</span> checked out
                                                         </p>
                                                         <p class="m-0">{{ \Carbon\Carbon::parse($ef->created_at)->format('M d')}}
                                                            at {{ \Carbon\Carbon::parse($ef->created_at)->format('g:i A ')}}
                                                         </p>
                                                         <div class="timeline-body">
                                                            <p class="content-block">
                                                            <p>{!!
                                                               App\BackendModel\Checkout_note::find($ef->note) ?  App\BackendModel\Checkout_note::find($ef->note)->name : ($ef->note??'');
                                                               !!}
                                                            </p>
                                                            <br>
                                                            @if($ef->location)
                                                               @if($ef->location->latitude && $ef->location->longitude)
                                                                 <p>  <a href="https://maps.google.com?q='{{$ef->location->latitude}},{{$ef->location->longitude}}'" target="blank" class="btn btn-bg-info"><i class="fa fa-map-marker" aria-hidden="true"></i></a> </p>
                                                               @endif
                                                            @endif
                                                          
                                                         </div>
                                                      </div>
                                                      <div class="col-md-3 pr-0">
                                                         <div class="timeline-action">
                                                           
                                                         </div>
                                                      </div>
                                                   </div>
                                                </div>
                                             </div>
                                          </li>
                                          <!----------------------Checkout ------------------->
                                          @elseif($ef->log_type==\App\BackendModel\EnquiryFollowup::ENQ_PURPOSE && \Getlead\Rolespermissions\Models\RolePermission::hasPermission('timeline.view.lead_updates'))
                                          @include('backend.user-pages.timeline.status.enquiry-purpose')
                                          @elseif($ef->log_type==\App\BackendModel\EnquiryFollowup::TYPE_NEW)
                                          @php $new_lead=1; @endphp
                                          <li class="timeline-inverted">
                                             <div class="timeline-badge">
                                                <i class="fa fa-file-text" style="margin-top: 10px;"></i>
                                             </div>
                                             <div class="timeline-panel">
                                                <div class="panel-title new-panel">New</div>
                                                <div class="timeline-header">
                                                   <div>
                                                      <div class="user-img profileImage"
                                                         style=" background: #D36DDD;color: #fff;">
                                                         {{ ($ef->added_by)? strtoupper(  substr($ef->added_by['vchr_user_name'], 0, 1)) : ''}}
                                                      </div>
                                                   </div>
                                                   <div class="timeline-heading">
                                                      <div class="col-md-9">
                                                         @if($enquiry->created_by_user)
                                                         <p class="timeline-title">
                                                            <span class="user">{{$ef->added_by ? $ef->added_by['vchr_user_name'] : ''}}</span>
                                                            created new <span class="task">lead via </span>
                                                            <strong class="pr-2">{{ $ef->source?$ef->source->vchr_enquiry_type:'' }}</strong>
                                                         </p>
                                                         @else
                                                         <p class="timeline-title"> New lead via <strong
                                                            class="pr-2">{{ $ef->source?$ef->source->vchr_enquiry_type:'' }}</strong>
                                                         </p>
                                                         @endif
                                                         <p class="m-0">{{ \Carbon\Carbon::parse($ef->created_at)->format('M d')}}
                                                            at {{ \Carbon\Carbon::parse($ef->created_at)->format('g:i A ')}}
                                                         </p>
                                                         @if($ef->enquiry->fb_ad_id!=NULL)
                                                         <div class="timeline-body">
                                                            <h6 class="title">Facebook Lead Info</h6>
                                                            @if($ef->enquiry->fb_ad_info!=NULL)
                                                            <p class="content-block">Lead ID
                                                               : {{$ef->enquiry->fb_ad_info->lead_id}} 
                                                            </p>
                                                            <p class="content-block">Add ID
                                                               : {{$ef->enquiry->fb_ad_info->ad_id}} 
                                                            </p>
                                                            <p class="content-block">Add Name
                                                               : {{$ef->enquiry->fb_ad_info->ad_name}} 
                                                            </p>
                                                            <p class="content-block">Name
                                                               : {{$ef->enquiry->fb_ad_info->full_name}} 
                                                            </p>
                                                            <p class="content-block">Email
                                                               : {{$ef->enquiry->fb_ad_info->email}} 
                                                            </p>
                                                            <p class="content-block">Campaign Name
                                                               : {{$ef->enquiry->fb_ad_info->campaign_name}} 
                                                            </p>
                                                            <p class="content-block">City : {{$ef->enquiry->fb_ad_info->city}} </p>
                                                            @endif
                                                         </div>
                                                         @endif
                                                      </div>
                                                      <div class="col-md-3 pr-0 text-right">
                                                         <div class="timeline-action">
                                                         
                                                         </div>
                                                      </div>
                                                   </div>
                                                </div>
                                             </div>
                                          </li>
                                          @else
                                          @include('backend.user-pages.timeline.status.activity-additional')
                                          @endif
                                          @endif
                                          @endforeach
                                          @endforeach
                                         
                                          @if($new_lead==0)
                                          <li class="timeline-inverted">
                                             <div class="timeline-badge">
                                                <i class="fa fa-file-text" style="margin-top: 10px;"></i>
                                             </div>
                                             <div class="timeline-panel">
                                                <div class="panel-title new-panel">New</div>
                                                <div class="timeline-header">
                                                   <div>
                                                      <div class="user-img profileImage"
                                                         style=" background: #D36DDD;color: #fff;">
                                                         @if($enquiry->created_by_user)
                                                         {{ strtoupper( substr($enquiry->created_by_user, 0, 1))}}
                                                         @endif
                                                      </div>
                                                   </div>
                                                   <div class="timeline-heading">
                                                      <div class="col-md-9">
                                                         @if($enquiry->created_by_user)
                                                         <p class="timeline-title">
                                                            <span class="user">{{$enquiry->created_by_user}}</span>
                                                            created new <span class="task">lead via </span>
                                                            <strong class="pr-2">{{ $enquiry->enquiry_type }}</strong>
                                                         </p>
                                                         @else
                                                         <p class="timeline-title"> New lead via <strong
                                                            class="pr-2">{{ $enquiry->enquiry_type }}</strong>
                                                         </p>
                                                         @endif
                                                         <p class="m-0">{{\App\Common\Variables::changeDateFormat($enquiry->created_at)}}
                                                            at {{\App\Common\Variables::changeTimeFormat($enquiry->created_at)}}
                                                         </p>
                                                         @if($enquiry->fb_ad_id!=NULL)
                                                         <div class="timeline-body">
                                                            <h6 class="title">Facebook Lead Info</h6>
                                                            @if($enquiry->fb_ad_info!=NULL)
                                                            <p class="content-block">Lead ID
                                                               : {{$enquiry->fb_ad_info->lead_id}} 
                                                            </p>
                                                            <p class="content-block">Add ID
                                                               : {{$enquiry->fb_ad_info->ad_id}} 
                                                            </p>
                                                            <p class="content-block">Add Name
                                                               : {{$enquiry->fb_ad_info->ad_name}} 
                                                            </p>
                                                            <p class="content-block">Name
                                                               : {{$enquiry->fb_ad_info->full_name}} 
                                                            </p>
                                                            <p class="content-block">Email
                                                               : {{$enquiry->fb_ad_info->email}} 
                                                            </p>
                                                            <p class="content-block">Campaign Name
                                                               : {{$enquiry->fb_ad_info->campaign_name}} 
                                                            </p>
                                                            <p class="content-block">City : {{$enquiry->fb_ad_info->city}} </p>
                                                            @endif
                                                         </div>
                                                         @endif
                                                      </div>
                                                      <div class="col-md-3 pr-0 text-right">
                                                         <div class="timeline-action">
                                                           
                                                         </div>
                                                      </div>
                                                   </div>
                                                </div>
                                             </div>
                                          </li>
                                          @endif
                                       </ul>
                                    </div>
                                    @else
                                    <div class="">
                                       <script src="https://unpkg.com/@lottiefiles/lottie-player@latest/dist/lottie-player.js"></script>
                                       <lottie-player src="{{asset('backend/images/activity-loader.json')}}"  background="transparent"  speed=".5"  style="width: 70%; height: 60%; margin: auto;"  loop autoplay></lottie-player>
  
                                    </div>
                                    @endif
                                 </div>
                              </div>
                           </div>
                        </div>
                     </div>
                  </div>
               </div>
            </div>
         </section>
      </div>
   </div>
</main>
<div class="preloader js-preloader flex-center">
   <script src="https://unpkg.com/@lottiefiles/lottie-player@latest/dist/lottie-player.js"></script>
   <lottie-player src="{{asset('backend/images/loader.json')}}"  background="transparent"  speed=".5"  style="width: 150px; height: 150px;"  loop autoplay></lottie-player>

</div>
{{------------------------------------------- Add Log Modal -----------------------------------------------}}
<div class="modal fade edit-task-modal" id="tab-log-detail"   data-toggle="modal" role="dialog" aria-labelledby="myModalLabel-task" aria-hidden="true">
   <div class="modal-dialog modal-lg modal-dialog-full" role="document">
      <div class="modal-content">
         <div class="modal-header" style="padding: 0px">
            <div class="row" style="width: 100%; margin: 0">
               <button type="button" class="close" id="closebutton" data-dismiss="modal" aria-label="Close" >
               <span aria-hidden="true">&times;</span>
               </button>
            </div>
         </div>
         <form class="myForm">
            <div class="modal-body row deal-pop-body" id="deal-detail-body">
               <div class="timeline-tab-inner">
                  <h3>Add Log</h3>
                  <div class="row mg-bt-15">
                     <div class="col-md-6 col-sm-12  ">
                        <label>Type</label>
                        <select class=" form-control select2" id="log-type"
                           style="width: 100%; height: 100%">
                           <option value="{{\App\BackendModel\EnquiryFollowup::TYPE_NOTE}}">Log a Note</option>
                           <option value="20">Log a file</option>
                           <option value="{{\App\BackendModel\EnquiryFollowup::TYPE_LOG_CALL}}">Log a Call</option>
                           <option value="{{\App\BackendModel\EnquiryFollowup::TYPE_LOG_EMAIL}}">Log a Email</option>
                           <option value="{{\App\BackendModel\EnquiryFollowup::TYPE_LOG_MEETING}}">Log a Meeting</option>
                        </select>
                     </div>
                     <div class="col-md-6 col-sm-12 logNote d-none">
                        <label>Outcome</label>
                        <select class="select2" style="width: 100%; height: 100%" id="log-outcome-val">
                           <option value="">Select an Outcome</option>
                           <option value="No Answer">No Answer</option>
                           <option value="Busy">Busy</option>
                           <option value="Wrong Number">Wrong Number</option>
                           <option value="Left Live Message">Left Live Message</option>
                           <option value="Left Voice Call">Left Voice Call</option>
                           <option value="Connected">Connected</option>
                        </select>
                        <span id="log-outcome-error" class="text-danger"></span>
                     </div>
                     <div class="col-md-6 col-sm-12 logNote d-none">
                        <div class="input-group with-addon-icon-left date-pick--div">
                           <div class="input-group with-addon-icon-left date-pick--div">
                              <!-- <input type="text" class="form-control datepicker" placeholder="Choose Date" id="log_date"> -->
                              <label>Date:</label>
                              <input type="text" class="form-control datepicker timeline-value-v2" data-tag="log_date" placeholder="Choose Date" name="log_date" autocomplete="off" id="datepicker-2">
                              {{-- <span class="input-group-append">
                              <span class="input-group-text"><i class="fa fa-calendar"></i></span>
                              </span> --}}
                           </div>
                           <span id="log-date-error" class="text-danger"></span>
                        </div>
                     </div>
                     <div class="col-md-6 col-sm-12 logNote d-none">
                        <div class="input-group with-addon-icon-left">
                           <label>Time:</label>
                           <input type="text" class="form-control time" placeholder="Time" id="log_time">
                           {{-- <span class="input-group-append">
                           <span class="input-group-text"><i class="fa fa-clock-o"></i></span>
                           </span> --}}
                        </div>
                        <span id="log-time-error" class="text-danger"></span>
                     </div>
                     <div class="col-md-12 col-sm-12 noteDiv">
                        <textarea name="note" class="summernote-2" id="log_note"></textarea>
                        <span id="log-note-error" class="text-danger"></span>
                        {{--
                        <div class="summernote-2"></div>
                        --}}
                     </div>
                     <div class="col-md-12 col-sm-12 uploadFile d-none">
                        <label>Upload file:</label>
                        <input type="file" name="log_file" id="log_file" class="form-control" placeholder="Upload file ( May contains .jpg,png,doc,pdf)">
                        <span id="log-file-error" class="text-danger"></span>
                     </div>
                     <div class="col-md-12 col-sm-12">
                        <div class="btn-holder">
                           <button class="main-round-btn btn_save_log" id="btn_save_log" type="button">
                              <!-- <i class="fa fa-save"></i> -->
                              Save
                           </button>
                           <a href="#" class="close task-close-btn" id="closebutton" data-target="#tab-log-detail" data-dismiss="modal" aria-label="Close">cancel </a>
                        </div>
                     </div>
                  </div>
               </div>
            </div>
         </form>
      </div>
   </div>
</div>
{{------------------------------------------- End -----------------------------------------------}}


{{------------------------------------------- Edit Log Note Modal -----------------------------------------------}}
<div class="modal fade edit-task-modal" id="lead-log-note-edit"   data-toggle="modal" role="dialog" aria-labelledby="myModalLabel-task" aria-hidden="true">
   <div class="modal-dialog modal-lg modal-dialog-full" role="document">
      <div class="modal-content">
         <div class="modal-header" style="padding: 0px">
            <div class="row" style="width: 100%; margin: 0">
               <button type="button" class="close" id="closebutton" data-dismiss="modal" aria-label="Close" >
               <span aria-hidden="true">&times;</span>
               </button>
            </div>
         </div>
         <form class="myForm" id="UpdateLogNote">
		 @csrf
		 
		 
            <div class="modal-body row deal-pop-body" id="deal-detail-body">
               <div class="timeline-tab-inner">
                  <h3>Edit Log Note</h3>
				  
				  <input type="hidden" name="LogNoteId" id="LogNoteId">
				  
                  <div class="row mg-bt-15">
                     <div class="col-md-6 col-sm-12  ">
                        <label>Type</label>
                        <select class=" form-control select2" id="log-type-edit" name="log_type_edit"
                           style="width: 100%; height: 100%">
                           <option value="{{\App\BackendModel\EnquiryFollowup::TYPE_NOTE}}">Log a Note</option>
                           <option value="20">Log a file</option>
                           <option value="{{\App\BackendModel\EnquiryFollowup::TYPE_LOG_CALL}}">Log a Call</option>
                           <option value="{{\App\BackendModel\EnquiryFollowup::TYPE_LOG_EMAIL}}">Log a Email</option>
                           <option value="{{\App\BackendModel\EnquiryFollowup::TYPE_LOG_MEETING}}">Log a Meeting</option>
                        </select>
                     </div>
                     <div class="col-md-6 col-sm-12 logNoteEdit d-none">
                        <label>Outcome</label>
                        <select class="select2" style="width: 100%; height: 100%" id="log-outcome-val">
                           <option value="">Select an Outcome</option>
                           <option value="No Answer">No Answer</option>
                           <option value="Busy">Busy</option>
                           <option value="Wrong Number">Wrong Number</option>
                           <option value="Left Live Message">Left Live Message</option>
                           <option value="Left Voice Call">Left Voice Call</option>
                           <option value="Connected">Connected</option>
                        </select>
                        <span id="log-outcome-error" class="text-danger"></span>
                     </div>
                     <div class="col-md-6 col-sm-12 logNoteEdit d-none">
                        <div class="input-group with-addon-icon-left date-pick--div">
                           <div class="input-group with-addon-icon-left date-pick--div">
                              <!-- <input type="text" class="form-control datepicker" placeholder="Choose Date" id="log_date"> -->
                              <label>Date:</label>
                              <input type="text" class="form-control datepicker timeline-value-v2" data-tag="log_date" placeholder="Choose Date" name="log_date" autocomplete="off" id="datepicker-2-edit">
                              {{-- <span class="input-group-append">
                              <span class="input-group-text"><i class="fa fa-calendar"></i></span>
                              </span> --}}
                           </div>
                           <span id="log-date-error" class="text-danger"></span>
                        </div>
                     </div>
                     <div class="col-md-6 col-sm-12 logNoteEdit d-none">
                        <div class="input-group with-addon-icon-left">
                           <label>Time:</label>
                           <input type="text" class="form-control time" placeholder="Time" id="log_time">
                           {{-- <span class="input-group-append">
                           <span class="input-group-text"><i class="fa fa-clock-o"></i></span>
                           </span> --}}
                        </div>
                        <span id="log-time-error" class="text-danger"></span>
                     </div>
                     <div class="col-md-12 col-sm-12 noteDivEdit">
                        <textarea name="note_edit" class="summernote-2" id="log-note-edit"></textarea>
                        <span id="log-note-error" class="text-danger"></span>
                        {{--
                        <div class="summernote-2"></div>
                        --}}
                     </div>
                     <div class="col-md-12 col-sm-12 uploadFileEdit d-none">
                        <label>Upload file:</label>
                        <input type="file" name="log_file" id="log_file" class="form-control" placeholder="Upload file ( May contains .jpg,png,doc,pdf)">
                        <span id="log-file-error" class="text-danger"></span>
                     </div>
                     <div class="col-md-12 col-sm-12">
                        <div class="btn-holder">
                           <button class="main-round-btn" id="btn_update_log" type="button">
                              <!-- <i class="fa fa-save"></i> -->
                              Update
                           </button>
                           <a href="#" class="close task-close-btn" id="closebutton" data-target="#tab-log-detail" data-dismiss="modal" aria-label="Close">cancel </a>
                        </div>
                     </div>
                  </div>
               </div>
            </div>
         </form>
      </div>
   </div>
</div>

{{------------------------------------------- End -----------------------------------------------}}


{{------------------------------------------- Add Task Modal ------------------------------------}}
<div class="modal fade edit-task-modal" id="tab-task-lead"   data-toggle="modal" role="dialog" aria-labelledby="myModalLabel-task" aria-hidden="true">
   <div class="modal-dialog modal-lg modal-dialog-full" role="document">
      <div class="modal-content">
         <div class="modal-header" style="padding: 0px">
            <div class="row" style="width: 100%; margin: 0">
               <button type="button" class="close" id="closebutton" data-dismiss="modal" aria-label="Close" >
               <span aria-hidden="true">&times;</span>
               </button>
            </div>
         </div>
         <div class="modal-body row deal-pop-body" id="deal-detail-body">
            <div class="timeline-tab-inner">
               <h3>Add Task</h3>
               <div class="row mg-bt-15">
                  <div class="col-md-6 col-sm-12">
                     <label>Task Name <span style="color: #ff0000">*</span></label>
                     <input class="form-control " id="task-name" name="" placeholder="Enter your task">
                     <span  style="font-size: 11px;" id="task-name-error" class="text-danger"></span>
                  </div>
                  <div class="col-md-6 col-sm-12  ">
                     <label>Category <span style="color: #ff0000">*</span></label>
                     <select class=" form-control select2" style="width: 100%; height: 100%" id="task-type-1">
                        <option value="">Select Category</option>
                        @foreach (\App\TaskCategory::whereNull('vendor_id')->orWhere('vendor_id',0)->orWhere('vendor_id',\App\User::getVendorId())->get() as $category)
                        <option value="{{$category->id}}">{{$category->name}}</option>
                        @endforeach
                     </select>
                     <span  style="font-size: 11px;" id="task-type-1-error" class="text-danger"></span>
                  </div>
                  <div class="col-md-6 col-sm-12 ">
                     <label>Date: <span style="color: #ff0000">*</span></label>
                     <div class="input-group with-addon-icon-left">
                        <div class="input-group ">
                           <input type="text" class="form-control datepicker timeline-value-v2" id="datepicker2" placeholder="Choose Date" name="log_date" autocomplete="off">
                           <span class="input-group-append">
                           <span class="input-group-text">
                           <i class="fa fa-calendar"></i>
                           </span>
                           </span>
                        </div>
                        <span  style="font-size: 11px;" id="datepicker-1-error" class="text-danger"></span>
                     </div>
                  </div>
                  <div class="col-md-6 col-sm-12 ">
                     <div class="input-group with-addon-icon-left">
                        <label>Time: <span style="color: #ff0000">*</span></label>
                        <div class="input-group with-addon-icon-left">
                           <input type="text" id="time-2" class="form-control time" placeholder="Time">
                           {{-- <span class="input-group-append">
                           <span class="input-group-text">
                           <i class="fa fa-clock-o"></i>
                           </span>
                           </span> --}}
                        </div>
                        <span  style="font-size: 11px;" id="time-1-error" class="text-danger"></span>
                     </div>
                     <span id="task-time-error" class="text-danger"></span>
                  </div>
                  <div class="col-md-6 col-sm-12">
                     <label>Asigned To: <span style="color: #ff0000">*</span></label> 
                     <select class=" form-control select2"
                        style="width: 100%; height: 100%" id="assigned-to-1">
                        <option value="">Assigned To</option>
                        @foreach($staffId as $staff)
                        <option value="{{ $staff->pk_int_user_id }}">{{ $staff->vchr_user_name }}</option>
                        @endforeach
                     </select>
                     <span style="font-size: 11px;" id="assigned-to-1-error" class="text-danger"></span>
                  </div>
                  <div class="col-md-12 col-sm-12">
                  <label>Description <span style="color: #ff0000">*</span></label>
                     <textarea name="task-description" id="task-description-1" class="form-control" placeholder="Description" rows="8"></textarea>
                     <span  style="font-size: 11px;" id="task-description-error-1" class="text-danger"></span>
                  </div>
                  <div class="col-md-12 col-sm-12">
                     <div class="btn-holder">
                        <button class="main-round-btn" id="btn_save_task" type="button" name="Save">
                           <!-- <i class="fa fa-save"></i> -->
                           Save
                        </button>
                        <a href="#" class="close task-close-btn" id="closebutton" data-target="#tab-task-lead" data-dismiss="modal" aria-label="Close">cancel </a>
                     </div>
                  </div>
               </div>
            </div>
         </div>
      </div>
   </div>
</div>
{{------------------------------------------- End -----------------------------------------------}}

{{------------------------------------------- Edit Task Modal -----------------------------------------------}}
<div class="modal fade edit-task-modal" id="task-lead-edit"   data-toggle="modal" role="dialog" aria-labelledby="myModalLabel-task" aria-hidden="true">
   <div class="modal-dialog modal-lg modal-dialog-full" role="document">
      <div class="modal-content">
         <div class="modal-header" style="padding: 0px">
            <div class="row" style="width: 100%; margin: 0">
               <button type="button" class="close" id="closebutton" data-dismiss="modal" aria-label="Close" >
               <span aria-hidden="true">&times;</span>
               </button>
            </div>
         </div>
         <div class="modal-body row deal-pop-body" id="deal-detail-body">
            <div class="timeline-tab-inner">
               <h3>Edit Task</h3>
               <div class="row mg-bt-15">
                  <div class="col-md-6 col-sm-12">
                     <label>Task Name</label>
                     <input class="form-control " id="task-name-edit" name="task-name-edit" placeholder="Enter your task">
                     <span  style="font-size: 11px;" id="task-name-error" class="text-danger"></span>
                  </div>
                  <div class="col-md-6 col-sm-12  ">
                     <label>Type</label>
                     <select class=" form-control select2" style="width: 100%; height: 100%" id="task-type-edit">
                        <option value="">Select Type</option>
                        @foreach (\App\TaskCategory::whereNull('vendor_id')->orWhere('vendor_id',0)->orWhere('vendor_id',\App\User::getVendorId())->get() as $category)
                        <option value="{{$category->id}}">{{$category->name}}</option>
                        @endforeach
                     </select>
                     <span  style="font-size: 11px;" id="task-type-error" class="text-danger"></span>
                  </div>
                  <div class="col-md-6 col-sm-12 ">
                     <div class="input-group with-addon-icon-left date-pick--div">
                        <div class="input-group with-addon-icon-left date-pick--div">
                           <label>Date:</label>
                              <input type="text" class="form-control datepicker timeline-value-v2" readonly id="datepicker1" placeholder="Choose Date" name="log_date_edit" autocomplete="off">
                           <span  style="font-size: 11px;" id="datepicker-2-error" class="text-danger"></span>
                        </div>
                        <span id="log-date-error" class="text-danger"></span>
                     </div>
                  </div>
                  <div class="col-md-6 col-sm-12 ">
                     <div class="input-group with-addon-icon-left">
                        <label>Time:</label>
                        <div class="input-group with-addon-icon-left">
                           <input type="text" id="time-2" class="form-control time time_edit" name="time_edit" placeholder="Time">
                        </div>
                        <span  style="font-size: 11px;" id="time-2-error" class="text-danger"></span>
                     </div>
                     <span id="log-time-error" class="text-danger"></span>
                  </div>
                  <div class="col-md-6 col-sm-12">
                     <label>Asigned To:</label> 
                     <select class=" form-control select2"
                        style="width: 100%; height: 100%" id="assigned-to-edit">
                        <option value="">Assigned To</option>
                        @foreach($staffId as $staff)
                        <option value="{{ $staff->pk_int_user_id }}">{{ $staff->vchr_user_name }}</option>
                        @endforeach
                     </select>
                     <span style="font-size: 11px;" id="assigned-to-error" class="text-danger"></span>
                  </div>
                  <div class="col-md-12 col-sm-12">
                     <textarea name="task-description" id="task-description-edit" class="form-control" placeholder="Description" rows="8"></textarea>
                     <span  style="font-size: 11px;" id="task-description-error" class="text-danger"></span>
                  </div>
                  <input type="hidden" name="tskId" id="tskId">
                  <div class="col-md-12 col-sm-12">
                     <div class="btn-holder">
                        <button class="main-round-btn" id="btn_submit_edit" type="button" name="update">Update</button>
                        <a href="#" class="close task-close-btn" id="closebutton" data-target="#tab-task-lead" data-dismiss="modal" aria-label="Close">Cancel</a>
                     </div>
                  </div>
               </div>
            </div>
         </div>
      </div>
   </div>
</div>
{{------------------------------------------- End -----------------------------------------------}}

{{---------------------------------------------- History Modal ------------------------------------------------------}}
<div class="modal fade edit-task-modal" id="lead-history-modal"   data-toggle="modal" role="dialog" aria-labelledby="myModalLabel-task" aria-hidden="true">
   <div class="modal-dialog modal-lg modal-dialog-full" role="document">
      <div class="modal-content">
         <div class="modal-header" style="padding: 0px">
            <div class="row" style="width: 100%; margin: 0">
               <button type="button" class="close" id="closebutton" data-dismiss="modal" aria-label="Close" >
               <span aria-hidden="true">&times;</span>
               </button>
            </div>
         </div>
         <div class="modal-body row deal-pop-body" id="deal-history">
            <div class="timeline-tab-inner">
               <h3>History</h3>
               <div class="history-col">
                  <div class="timeline-history">
                     <p class="history-title"><span style="font-weight: 500">Getlead demo</span> changed  <span>scheduled date</span> to <span>10-05-2022</span></p>
                     <p class="history-date">updated on Jan 14 at 2:40 PM</p>
                  </div>
                  <div class="timeline-history">
                     <p class="history-title"><span style="font-weight: 500">Getlead demo</span> changed  <span>scheduled date</span> to <span>10-05-2022</span></p>
                     <p class="history-date">updated on Jan 14 at 2:40 PM</p>
                     <p class="history-body">Lorem Ipsumis simply dummy text of the printing and typesetting industry. </p>
                  </div>
                  <div class="timeline-history">
                     <p class="history-title"><span style="font-weight: 500">Getlead demo</span> changed  <span>scheduled date</span> to <span>10-05-2022</span></p>
                     <p class="history-date">updated on Jan 14 at 2:40 PM</p>
                  </div>
                  <div class="timeline-history">
                     <p class="history-title"><span style="font-weight: 500">Getlead demo</span> changed  <span>scheduled date</span> to <span>10-05-2022</span></p>
                     <p class="history-date">updated on Jan 14 at 2:40 PM</p>
                     <p class="history-body">Lorem Ipsumis simply dummy text of the printing and typesetting industry. </p>
                  </div>
                  <div class="timeline-history">
                     <p class="history-title"><span style="font-weight: 500">Getlead demo</span> changed  <span>Task status</span> to <span>Completed</span></p>
                     <p class="history-date">updated on Jan 14 at 2:40 PM</p>
                     <p class="history-body">Lorem Ipsumis simply dummy text of the printing and typesetting industry. </p>
                  </div>
               </div>
            </div>
         </div>
      </div>
   </div>
</div>
{{--------------------------------------------------- End --------------------------------------------------}}

{{---------------------------------------------- Email Modal ------------------------------------------------------}}
<div class="modal fade edit-task-modal" id="lead-email-modal"   data-toggle="modal" role="dialog" aria-labelledby="myModalLabel-task" aria-hidden="true">
   <div class="modal-dialog modal-lg modal-dialog-full" role="document">
      <div class="modal-content">
         <div class="modal-header" style="padding: 0px">
            <div class="row" style="width: 100%; margin: 0">
               <button type="button" class="close" id="closebutton" data-dismiss="modal" aria-label="Close" >
               <span aria-hidden="true">&times;</span>
               </button>
            </div>
         </div>
         <div class="modal-body row deal-pop-body" id="deal-detail-body">
            <div class="timeline-tab-inner">
               <h3>Add Email</h3>
               <div class="row mg-bt-15">
                  <div class="col-md-6 col-sm-12  ">
                     <label>Type</label>
                     <select class=" form-control select2" id="log-type"
                        style="width: 100%; height: 100%">
                        <option value="{{\App\BackendModel\EnquiryFollowup::TYPE_NOTE}}">Log a Note</option>
                        <option value="{{\App\BackendModel\EnquiryFollowup::TYPE_LOG_CALL}}">Log a Call</option>
                        <option value="{{\App\BackendModel\EnquiryFollowup::TYPE_LOG_EMAIL}}">Log a Email</option>
                        <option value="{{\App\BackendModel\EnquiryFollowup::TYPE_LOG_MEETING}}">Log a Meeting</option>
                     </select>
                  </div>
                  <div class="col-md-6 col-sm-12 ">
                     <label>Outcome</label>
                     <select class="select2" style="width: 100%; height: 100%" id="log-outcome-val">
                        <option value="">Select an Outcome</option>
                        <option value="No Answer">No Answer</option>
                        <option value="Busy">Busy</option>
                        <option value="Wrong Number">Wrong Number</option>
                        <option value="Left Live Message">Left Live Message</option>
                        <option value="Left Voice Call">Left Voice Call</option>
                        <option value="Connected">Connected</option>
                     </select>
                     <span id="log-outcome-error" class="text-danger"></span>
                  </div>
                  <div class="col-md-6 col-sm-12 ">
                     <div class="input-group with-addon-icon-left date-pick--div">
                        <div class="input-group with-addon-icon-left date-pick--div">
                           <!-- <input type="text" class="form-control datepicker" placeholder="Choose Date" id="log_date"> -->
                           <label>Date:</label>
                           <input type="text" class="form-control datepicker timeline-value-v2" data-tag="log_date" placeholder="Choose Date" name="log_date" autocomplete="off" id="datepicker-2">
                           {{-- <span class="input-group-append">
                           <span class="input-group-text"><i class="fa fa-calendar"></i></span>
                           </span> --}}
                        </div>
                        <span id="log-date-error" class="text-danger"></span>
                     </div>
                  </div>
                  <div class="col-md-6 col-sm-12 ">
                     <div class="input-group with-addon-icon-left">
                        <label>Time:</label>
                        <input type="text" class="form-control time" placeholder="Time" id="log_time">
                        {{-- <span class="input-group-append">
                        <span class="input-group-text"><i class="fa fa-clock-o"></i></span>
                        </span> --}}
                     </div>
                     <span id="log-time-error" class="text-danger"></span>
                  </div>
                  <div class="col-md-12 col-sm-12">
                     <textarea name="note" class="summernote-2" id="log_note"></textarea>
                     <span id="log-note-error" class="text-danger"></span>
                     {{--
                     <div class="summernote-2"></div>
                     --}}
                  </div>
                  <div class="col-md-12 col-sm-12">
                     <div class="btn-holder">
                        <button class="main-round-btn" id="btn_save_log" type="button">
                           <!-- <i class="fa fa-save"></i> -->
                           Save
                        </button>
                        <a href="#" class="close task-close-btn" id="closebutton" data-target="#tab-log-detail" data-dismiss="modal" aria-label="Close">cancel </a>
                     </div>
                  </div>
               </div>
            </div>
         </div>
      </div>
   </div>
</div>
{{--------------------------------------------------- End --------------------------------------------------}}
<div class="modal fade" id="reschedule_tasks_modal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel"
   aria-hidden="true">
   <form id="taskReschedule" enctype="multipart/form-data">
      <div class="modal-dialog" role="document">
         <div class="modal-content">
            <div class="modal-header text-center">
               <h4 class="modal-title w-100 font-weight-bold g-clr">Reschedule Task</h4>
               <button type="button" class="close" data-dismiss="modal" aria-label="Close">
               <span aria-hidden="true">&times;</span>
               </button>
            </div>
            <div class="modal-body mx-3">
               <div class="row">
                  <div class="col-md-6 col-sm-12 md-form mb-1">
                     <label for="default-input" class="form-control-label">Scheduled Date</label>
                     <input type="text" name="re_scheduled_date" class="form-control date" id="re_scheduled_date" placeholder="Select reschedule date" autocomplete="off" required>
                     <span class="error re_scheduled_date"></span>
                  </div>
                  <div class="col-md-6 col-sm-12 ">
                     <div class="input-group with-addon-icon-left">
                        <label>Time:</label>
                        <div class="input-group with-addon-icon-left">
                           <input type="text" id="time-2" name="time_task" class="form-control time" placeholder="Time" required>
                           <span class="input-group-append">
                           <span class="input-group-text">
                           <i class="fa fa-clock-o"></i>
                           </span>
                           </span>
                        </div>
                        <span  style="font-size: 11px;" id="time-2-error" class="text-danger"></span>
                     </div>
                     <span id="log-time-error" class="text-danger"></span>
                  </div>
               </div>
               <div class="md-form mb-1">
                  <label for="default-input" class="form-control-label">Reason for reschedule</label>
                  <textarea name="reason" class="form-control" id="reason" placeholder="Reason for reschedule" autocomplete="off" height="100px"></textarea>
                  <span class="error reason"></span>
               </div>
            </div>
            <input type="hidden" name="re_task_id" id="re_task_id">
            <div class="modal-footer d-flex justify-content-center">
               <button class="main-round-btn">Reschedule</button>
            </div>
         </div>
      </div>
   </form>
</div>
{{---------------------------------------------- Task reschedule Modal ------------------------------------------------------}}
<div class="modal fade" id="reschedule_tasks_modal"   data-toggle="modal" role="dialog" aria-labelledby="myModalLabel-task" aria-hidden="true">
   <div class="modal-dialog modal-lg modal-dialog-full" role="document">
      <div class="modal-content">
         <div class="modal-header" style="padding: 0px">
            <div class="row" style="width: 100%; margin: 0">
               <button type="button" class="close" id="closebutton" data-dismiss="modal" aria-label="Close" >
               <span aria-hidden="true">&times;</span>
               </button>
            </div>
         </div>
         <div class="modal-body row deal-pop-body" id="deal-history">
            <div class="timeline-tab-inner">
               <h3>Task Reschedule</h3>
               <div class="history-col">
                  <form id="taskReschedule" enctype="multipart/form-data">
                     <div class="modal-dialog" role="document">
                        <div class="modal-content">
                           <div class="modal-header text-center">
                              <h4 class="modal-title w-100 font-weight-bold g-clr">Reschedule Task</h4>
                              <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                              <span aria-hidden="true">&times;</span>
                              </button>
                           </div>
                           <div class="modal-body mx-3">
                              <div class="md-form mb-1">
                                 <label for="default-input" class="form-control-label">Scheduled Date</label>
                                 <input type="text" name="re_scheduled_date" class="form-control date" id="re_scheduled_date" placeholder="Select reschedule date" autocomplete="off">
                                 <span class="error re_scheduled_date"></span>
                              </div>
                              <div class="md-form mb-1">
                                 <label for="default-input" class="form-control-label">Reason for reschedule</label>
                                 <textarea name="reason" class="form-control" id="reason" placeholder="Reason for reschedule" autocomplete="off" height="100px"></textarea>
                                 <span class="error reason"></span>
                              </div>
                           </div>
                           <input type="hidden" name="re_task_id" id="re_task_id">
                           <div class="modal-footer d-flex justify-content-center">
                              <button class="main-round-btn">Reschedule</button>
                           </div>
                        </div>
                     </div>
                  </form>
               </div>
            </div>
         </div>
      </div>
   </div>
</div>
{{--------------------------------------------------- End --------------------------------------------------}}

<!-- deals modal    -->

@include('gl-crm.pages.user.deals.add-deal')

@endsection
@push('footer.script')
<script src="{{ url('/js/accordin.js') }}"></script>
<script src="{{ url('/js/jquery.preloadinator.min.js')}}"></script>
<script type="text/javascript" src="{{ url('backend/js/daterangepicker.js')}}"></script>
<script src="{{url('js/bootstrap-datetimepicker.min.js')}}"></script>
<!-- <script>
   $('.js-preloader').preloadinator({
       minTime: 2000
   });
   </script>  -->
<script>
   $("#datepicker2").datepicker({minDate: new Date(),dateFormat: 'd-m-yy'});
   
   $("#datepicker1").datepicker({dateFormat: 'dd/mm/yy'});
   $("#re_scheduled_date").datepicker({minDate: new Date(), dateFormat: 'dd/mm/yy'});
   function logNoteSummernoteInit()
   {
      $('.summernote-2').summernote({
          height: '100',
          toolbar: [
              ['style', ['bold', 'italic', 'underline', 'clear']],
              ['font', ['strikethrough', 'superscript', 'subscript']],
              ['fontsize', ['fontsize']],
              ['color', ['color']],
              ['para', ['ul', 'ol', 'paragraph']],
              ['height', ['height']]
          ],
          disableDragAndDrop: true
      });
   }
   
   function logNoteSummernoteDestroy()
   {
      $('.summernote-2').summernote('destroy');
   }
   
   function resetLogNoteForm()
   {
      $('#log_note').val('');
      $('#log-outcome-val').val('');
      $('#log_date').val('');
      $('#log_time').val('');
   
      $('#log-note-error').text("");
   
      $('#log-outcome-val').select2('destroy');
      $('.select2').select2();
   
      logNoteSummernoteDestroy();
      logNoteSummernoteInit();
   }
   
   function appendToTimelineStatus(data,type) {
      if (data.status_log_html) {
        switch (type) {
           case 'log':
              if ($(".timeline-period-recent").length == 0) {
                    $(".timeline-log").prepend('<li class="timeline-period timeline-period-recent">Recent</li>');
                    $(".activity-timeline").prepend('<li class="timeline-period timeline-period-recent">Recent</li>');
              }
        
              $(data.status_log_html).insertAfter(".timeline-period-recent");
              break;
           case 'activity':
              if ($(".timeline-period-recent").length == 0) {
                    $(".activity-timeline").prepend('<li class="timeline-period timeline-period-recent">Recent</li>');
              }
        
              $(data.status_log_html).insertAfter(".timeline-period-recent");
              break;
   
           default:
              if ($(".timeline-period-recent").length == 0) {
                    $(".activity-timeline").prepend('<li class="timeline-period timeline-period-recent">Recent</li>');
              }
        
              $(data.status_log_html).insertAfter(".timeline-period-recent");
              break;
        }
          
      }
   }
</script>
<script type="text/javascript">
   /* -------- Start Purpose vise additional field ----- */
   value = @json($enquiry->fk_int_purpose_id);
   getAdditionalData(value);
   /* -------- End Purpose vise additional field ----- */
   

   $('.js-preloader').preloadinator({
     minTime: 2000
   });

 
   $(document).on('click', '.edit-profile', function () {
     $('#lead_name').val(document.getElementById('name').value);
     $('#lead_email').val(document.getElementById('email').value);
     $('#lead_mobile').val(document.getElementById('mobile').value);
     $('#lead_id').val(document.getElementById('id').value);
   });
   $(document).on('submit', '#profile_edit', function (event) {
     BASE_URL = {!! json_encode(url('/')) !!}
     $('#edit_spin').show();
     event.preventDefault();
     $.ajax({
         url: BASE_URL + '/user/update-profile',
         type: 'POST',
         dataType: 'JSON',
         data: new FormData(this),
         contentType: false,
         processData: false,
     })
         .done(function (res) {
             if (res.status == 'success') {
                 $('#edit_spin').hide();
                 $("#profile_edit")[0].reset();
                 $('#edit_profile').modal('toggle');
                 window.location.href = '/user/enquiry-timeline/' + document.getElementById('id').value
                 $.alert({
                     title: 'Success',
                     type: 'green',
                     content: res.msg,
                 });
             } else {
                 $('#edit_spin').hide();
             }
         })
         .fail(function () {
         })
   });
   
   $(document).ready(function () {
      $('.date').datepicker({
         dateFormat: 'yy-mm-dd',
         autoclose: true,
      });
 
      $("#start_date1").datepicker({
         changeMonth: true,
         changeYear: true,
         dateFormat: 'yy-mm-dd',
         minDate:0,
         buttonText: '<i class="fa fa-calendar"></i>',
         beforeShow: function(input) {
            $(input).datepicker("widget").attr('hide-calendar');
         },
         onSelect: function(selected) {
            $("#end_date1").datepicker("option","minDate", selected)
         }
      });
      
      $("#end_date1").datepicker({ 
         changeMonth: true,
         changeYear: true,
         dateFormat: 'yy-mm-dd',
         minDate:0,
         buttonText: '<i class="fa fa-calendar"></i>',
         onSelect: function(selected) {
         // $("#start_date").datepicker("option","maxDate", selected)
         }
      }); 

      //Validate mobile number
      $('.customer_mobile').on('change',function(){
          $.ajax({
              url: BASE_URL + '/user/validate-leads-mobile',
              type: 'POST',
              dataType: 'JSON',
              data:{
                  mobile:$(this).val(),
                  id:$('#pk_int_enquiry_id').val()
              }
          })
          .done(function(res) {
              if(res == '1'){
                  alert('Mobile Number Already Exist');
              }
          })
          .fail(function() {
          });
      });
   
     $('#edit_spin').hide();
   
     /*----------- Front End Scripts ----------*/
   
     $(".nav-tabs a").click(function () {
         var position = $(this).parent().position();
         var width = $(this).parent().width();
         $(".slider").css({
             "left": +position.left,
             "width": width
         });
     });
     var actWidth = $(".nav-tabs").find(".active").parent("li").width();
     var actPosition = $(".nav-tabs .active").position();
     $(".slider").css({
         "left": +actPosition.left,
         "width": actWidth
     });
     $('.select2').select2();
     width: 'resolve'
     $('.summernote-1').summernote({
         height: '215',
         toolbar: [
             ['style', ['bold', 'italic', 'underline', 'clear']],
             ['font', ['strikethrough', 'superscript', 'subscript']],
             ['fontsize', ['fontsize']],
             ['color', ['color']],
             ['para', ['ul', 'ol', 'paragraph']],
             ['height', ['height']]
         ],
         disableDragAndDrop: true
     });
   
     logNoteSummernoteInit();
   
     $('.summernote-3').summernote({
         height: '125',
         toolbar: [
             ['style', ['bold', 'italic', 'underline', 'clear']],
             ['font', ['strikethrough', 'superscript', 'subscript']],
             ['fontsize', ['fontsize']],
             ['color', ['color']],
             ['para', ['ul', 'ol', 'paragraph']],
             ['height', ['height']]
         ],
         disableDragAndDrop: true
     });
     /*----------- Front End Scripts ----------*/
     /*----------- Reset Note----------*/
   
     $('#note_reset_values').click(function () {
   
         resetNotes();
     });
   
     function resetNotes() {
         $('#note').val('');
     }
   
     /*----------- Reset Task----------*/
   
   
     $('#task_reset_values').click(function () {
         resetTask();
     });
   
     /*----------- Reset Schedule----------*/
   
     $('#schedule_reset_values').click(function () {
         resetSchedule();
     });
   
     function resetSchedule() {
         $('#note').val('');
     }
   
     /*--------------------------------Status start------------------------------------*/
   
     $("#timeline-enq-purpose").change(function () {
         var enq_purpose = $('#timeline-enq-purpose').val();
         var id = $('#enqId').val();
         $.ajaxSetup({
            headers: {
               'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            }
         });
         $.post("{!! url('user/add-timeline-enq-purpose')!!}", {
            enq_purpose: enq_purpose,
            id: id
         }, function (data) {
   
         toastr.success('Enquiry purpose updated  successfully');
            resetNotes();
            // loadPage();
            appendToTimelineStatus(data,'activity');
            // loadPage();

         });
   
     });
   
     /*--------------------------------Status end------------------------------------*/
   
   
     /*--------------------------------Status start------------------------------------*/
   
     $("#timeline-status").change(function () {
         var status = $('#timeline-status').val();
         var id = $('#enqId').val();
         $.ajaxSetup({
            headers: {
               'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            }
         });
         $.post("{!! url('user/add-timeline-status')!!}", {
            status: status,
            id: id
         }, function (data) {
            
            toastr.success('Status changed successfully');
            resetNotes();
            // loadPage();
            appendToTimelineStatus(data,'activity');
            // loadPage();
         });
   
     });
   
     /*--------------------------------Status end------------------------------------*/

     /*----------------------agency------------*/

     $("#timeline-agency").change(function () {
         var agency = $('#timeline-agency').val();
         var id = $('#enqId').val();
         $.ajaxSetup({
            headers: {
               'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            }
         });
         $.post("{!! url('user/add-timeline-agency')!!}", {
            agency: agency,
            id: id
         }, function (data) {
            
            toastr.success('Agency changed successfully');
            resetNotes();
            // loadPage();
            appendToTimelineStatus(data,'activity');
            // loadPage();
         });
   
     });

     /* ----------------------Lead type update------------------ */

     $("#timeline-enq-type").change(function () {
         var lead_type = $('#timeline-enq-type').val();
         var id = $('#enqId').val();
         $.ajaxSetup({
            headers: {
               'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            }
         });
         $.post("{!! url('user/add-timeline-enq-type')!!}", {
            lead_type: lead_type,
            id: id
         }, function (data) {
         toastr.success('Enquiry type updated  successfully');
            resetNotes();
            // loadPage();
            appendToTimelineStatus(data,'activity');
            // loadPage();
         });
   
     });


     /*--------------------------------Tab notes start------------------------------------*/
   
     /* Add Note */
   
     $('#btn_save_note').on('click', function () {
      
         var note = $('.summernote-1').val();
         var id = $('#enqId').val();
       
         if (note !== '') {
            $('#btn_save_note').html("Saving...");
            $('#btn_save_note').prop('disabled', true).css('opacity',.4);
             $.post("{!! url('user/add-timeline-note')!!}", {
                 note: note,
                 id: id
             }, function (data) {
                 if (data.status === true) {
                     toastr.success('A note logged successfully');
                     $('.summernote-1').val('');
                     $('#btn_save_note').html("Save");
                     $('#btn_save_note').prop('disabled', false).css('opacity',1);
                     loadPage();
                 }
                
             });
         } else {
             $('#summerNote-error').text('* Required Field');
         }
     });
   
   
     /*--------------------------------Tab notes end------------------------------------*/
   
     /*--------------------------------Tab Log Activity Start----------------------------*/
   
   /* Change log type */
   $('#log-type').on('change', function () {
      var logType = $(this).val();
      var $logOutcomeVal = $('#log-outcome-val');
      var $logNote = $('.logNote');
      var $uploadFile = $('.uploadFile');
      var $noteDiv = $('.noteDiv');

      $logNote.removeClass('d-none');
      $uploadFile.addClass('d-none');
      $noteDiv.removeClass('d-none');

      if (logType == 2 || logType == 4) {
         $logOutcomeVal.show();
      } else if (logType == 1) {
         $logNote.addClass('d-none');
      } else if (logType == 20) {
         $uploadFile.removeClass('d-none');
         $noteDiv.addClass('d-none');
         $logNote.addClass('d-none');
      } else {
         $logOutcomeVal.hide();
      }
   });

   $(function () {
   $('[data-toggle="tooltip"]').tooltip()
   })
   
     /* Save logs */
     $(document).on('click','#btn_save_log',function(){
      $('.btn_save_log').html("Saving...");
      $('.btn_save_log').prop('disabled', true).css('opacity',.4);
		 var logType = $('#log-type').val();
         var formData = new FormData();
         var fileInput = document.getElementById('log_file');
         formData.append('file', fileInput.files[0]);

         // Append custom parameters
         formData.append('note', $('#log_note').val());
         formData.append('enquiry_id', $('#enqId').val());
         formData.append('log_type', logType);
         formData.append('answer', $('#log-outcome-val').val());
         formData.append('date', $('#datepicker-2').val());
         formData.append('time', $('#log_time').val());
         formData.append('file', $('#log_file').val());
      
         var url = "{!! url('user/add-timeline-log')!!}";
         if (logType === '{{\App\BackendModel\EnquiryFollowup::TYPE_LOG_CALL}}' && $('#log-outcome-val').val() !== '') {
             $.post({
               url: url,
               data: formData,
               processData: false,
               contentType: false,
               success: function(data) {
                  if (data.status === true) {
                  $("#tab-log-detail").modal('hide')
      
                     toastr.success('A call logged successfully');
   
                     resetLogNoteForm();
                    
                     appendToTimelineStatus(data,'log');
                     // $('#btn_save_task').html("Save");
                     // $('#btn_save_task').prop('disabled', false).css('opacity',1);
                     loadPage();
                     
                 }
               },
               error: function(xhr, status, error) {
                  // Error occurred during file upload
                  console.error('Error: ' + xhr.status);
               }
            });
         } else if (logType !== '') {
            $.post({
               url: url,
               data: formData,
               processData: false,
               contentType: false,
               success: function(data) {
                  if (data.status === true) {
                  $("#tab-log-detail").modal('hide')
                     toastr.success('Log added successfully');
   
                     resetLogNoteForm();
                   
                     appendToTimelineStatus(data,'log');
                   
                     loadPage();
                 }
               },
               error: function(xhr, status, error) {
                  // Error occurred during file upload
                  console.error('Error: ' + xhr.status);
               }
            });
         }
     });
     /*--------------------------------Tab Log Activity End------------------------------*/
   
     /*--------------------------------Tab Task Start------------------------------*/
   
     $('#btn_save_task').on('click', function () {
         taskName = $('#task-name').val();
         description = $('#task-description-1').val();
         taskType = $('#task-type-1').val();
         assignedTo = $('#assigned-to-1').val();
         reminder = $('#reminder').val();
         id = $('#enqId').val();
         time = $('#time-2').val();
         date = $('#datepicker2').val();
         
         // flag =false;
         if (taskName === "") {
             $('#task-name-error').text('* Required Field')
             $('#task-description-error').text('');
             $('#datepicker-1-error').text('');
             $('#time-1-error').text('');
             $('#assigned-to-1-error').text('');
             $('#task-type-1-error').text('');
             $('#reminder-error').text('');
             $('#btn_save_task').html("Save");
             $('#btn_save_task').prop('disabled', false);
             return false;
         }
         if (date === '') {
             $('#datepicker-1-error').text('* Required Field');
             $('#task-name-error').text('');
             $('#task-description-error-1').text('');
             $('#time-1-error').text('');
             $('#assigned-to-1-error').text('');
             $('#task-type-1-error').text('');
             $('#reminder-error').text('');
             $('#btn_save_task').html("Save");
             $('#btn_save_task').prop('disabled', false);
             return false;
   
         } 
         if (time === "") {
             $('#time-1-error').text('* Required Field');
             $('#task-name-error').text('');
             $('#datepicker-1-error').text('');
             $('#task-description-error-1').text('');
             $('#assigned-to-1-error').text('');
             $('#task-type-1-error').text('');
             $('#reminder-error').text('');
             $('#btn_save_task').html("Save");
             $('#btn_save_task').prop('disabled', false);
             return false;
         }
          if (description === "") {
             $('#task-description-error-1').text('* Required Field');
             $('#task-name-error').text('');
             $('#datepicker-1-error').text('');
             $('#time-1-error').text('');
             $('#assigned-to-1-error').text('');
             $('#task-type-1-error').text('');
             $('#reminder-error').text('');
             $('#btn_save_task').html("Save");
             $('#btn_save_task').prop('disabled', false);
             return false;
         } 
         if (taskType === "") {
             $('#task-type-1-error').text('* Required Field');
             $('#task-name-error').text('');
             $('#datepicker-1-error').text('');
             $('#time-1-error').text('');
             $('#assigned-to-1-error').text('');
             $('#task-description-error-1').text('');
             $('#reminder-error').text('');
             $('#btn_save_task').html("Save");
             $('#btn_save_task').prop('disabled', false);
             return false;
         }
         
         // if(flag)
         //    return false;
            
         if (taskName != '' && description != '' && date != '' && time != '') {
            $('#btn_save_task').html("Saving...");
            $('#btn_save_task').prop('disabled', true).css('opacity',.4);
             $.post("{!! url('user/add-timeline-task')!!}", {
                 id: id,
                 description: description,
                 name: taskName,
                 task: taskType,
                 user_id: assignedTo,
                 reminder: reminder,
                 date: date,
                 time: time,
             }, function (data) {
                  if(data.status == 'success'){
                     toastr.success('A task added successfully');
                     resetTask();
                     loadPage();
                  }else{
                     toastr.error(data.msg);
                  }
                  $('#btn_save_task').html("Save");
                  $('#btn_save_task').prop('disabled', false).css('opacity',1);
             });
         }
     });
   
     /*--------------------------------Tab Task Activity End------------------------------*/
   
     /*--------------------------------Tab Schedule Start ------------------------------*/
     $('#btn_save_schedule').on('click', function () {
         $('#btn_save_schedule').html("saving");
         $('#btn_save_schedule').prop('disabled', true);
         scheduleName = $('#schedule-name').val();
         note = $('#txtEditor-4').val();
         duration = $('#duration').val();
         time = $('#time-3').val();
         id = $('#enqId').val();
         date = $('#datepicker-3').val();
         var follow_up = $('#follow_up').val();
         if (scheduleName === "") {
             $('#schedule-name-error').text('* Required Field')
             $('#txtEditor-4-error').text('')
             $('#duration-error').text('')
             $('#datepicker-3-error').text('')
             $('#time-3-error').text('') -
             $('#btn_save_schedule').html("Save");
             $('#btn_save_schedule').prop('disabled', false);
         } else if (duration === "") {
             $('#duration-error').text('* Required Field')
             $('#schedule-name-error').text('')
             $('#txtEditor-4-error').text('')
             $('#datepicker-3-error').text('')
             $('#time-3-error').text('')
             $('#btn_save_schedule').html("Save");
             $('#btn_save_schedule').prop('disabled', false);
         } else if (date === "") {
             $('#datepicker-3-error').text('* Required Field')
             $('#duration-error').text('')
             $('#schedule-name-error').text('')
             $('#txtEditor-4-error').text('')
             $('#time-3-error').text('')
             $('#btn_save_schedule').html("Save");
             $('#btn_save_schedule').prop('disabled', false);
         } else if (time === "") {
             $('#time-3-error').text('* Required Field')
             $('#txtEditor-4-error').text('* Required Field')
             $('#datepicker-3-error').text('')
             $('#duration-error').text('')
             $('#schedule-name-error').text('')
             $('#btn_save_schedule').html("Save");
             $('#btn_save_schedule').prop('disabled', false);
         } else if (note === "") {
             $('#txtEditor-4-error').text('* Required Field')
             $('#datepicker-3-error').text('')
             $('#duration-error').text('')
             $('#schedule-name-error').text('')
             $('#time-3-error').text('')
             $('#btn_save_schedule').html("Save");
             $('#btn_save_schedule').prop('disabled', false);
         } else if (scheduleName != '' && note != '' && duration != '' && time != '' && date != '') {
             $.post("{!! url('user/add-timeline-schedule')!!}", {
                 id: id,
                 name: scheduleName,
                 note: note,
                 date: date,
                 time: time,
                 duration: duration,
                 followup_required: follow_up
             }, function (data) {
                 toastr.success('A task scheduled successfully');
                 resetSchedule();
                 loadPage();
             });
         }
     });
   
     /*--------------------------------Tab Email------------------------------*/
     $(document).on('submit', '#sendMail', function (event) {
         $('#addmail').html("sending");
         $('#addmail').prop('disabled', true);
         event.preventDefault();
         $('.error').html('');
         $('.error').hide();
         id = $('#enqId').val();
         BASE_URL = window.location.origin;
   
         $.ajax({
             url: BASE_URL + '/user/timeline-send-save-email/' + id,
             type: 'POST',
             dataType: 'JSON',
   
             data: new FormData(this),
             contentType: false,
             processData: false,
         }).done(function (res) {
             if (res.status === 'success') {
                 $('#addmail').html("Send");
                 $('#addmail').prop('disabled', false);
                 $.alert({
                     title: 'Success',
                     type: 'green',
                     content: res.msg,
                 });
                 resetNotes();
                 loadPage();
             } else if (res.status === 'fail') {
                 $('#addmail').html("Send");
                 $('#addmail').prop('disabled', false);
                 $.alert({
                     title: 'Fail',
                     type: 'red',
                     content: res.msg,
                 });
                 resetNotes();
               //   loadPage();
               $('#addmail').html('<i class="fa fa-paper-plane"></i>Send');
               $('#addmail').prop('disabled', false);
             } else {
   
                 $.each(res.msg, function (index, val) {
   
                     console.log(index);
                     $('#addmail').html("Send");
                     $('#addmail').prop('disabled', false);
                     $("#sendMail")[0].reset();
   
                     console.log(val);
                     console.log(index);
                     $('.' + index).html(val);
                     $('.' + index).show();
                 });
   
             }
         })
             .fail(function () {
             })
             .always(function (com) {
                 $('#enquiry-info-table').DataTable().ajax.reload(null, false);
   
             });
     });
     $(document).on('change', '#follow_up', function () {
         if ($('#follow_up').val() == 0) {
             var scheduleName = $('#schedule-name').val();
             var note = $('#txtEditor-4').val();
             var duration = $('#duration').val();
             var time = $('#time-3').val();
             var date = $('#datepicker-3').val();
             var follow_up = $('#follow_up').val();
             id = $('#enqId').val();
             if (scheduleName === "") {
                 $('#schedule-name-error').text('* Schedule title is required ')
                 $('#txtEditor-4-error').text('')
                 $('#duration-error').text('')
                 $('#datepicker-3-error').text('')
                 $('#time-3-error').text('')
                 $('#follow_up').val('');
             } else if (duration === "") {
                 $('#duration-error').text('* Duration is required')
                 $('#schedule-name-error').text('')
                 $('#txtEditor-4-error').text('')
                 $('#datepicker-3-error').text('')
                 $('#time-3-error').text('')
                 $('#follow_up').val('');
             } else if (date === "") {
                 $('#datepicker-3-error').text('* Date is required')
                 $('#duration-error').text('')
                 $('#schedule-name-error').text('')
                 $('#txtEditor-4-error').text('')
                 $('#time-3-error').text('')
                 $('#follow_up').val('');
             } else if (scheduleName != '' && duration != '' && date != '') {
                 $.confirm({
                     title: 'Followup not required',
                     content: 'Are you sure you want to sure ?',
                     icon: 'la la-question-circle',
                     animation: 'scale',
                     closeAnimation: 'scale',
                     opacity: 0.5,
                     buttons: {
                         'confirm': {
                             text: 'Proceed',
                             btnClass: 'btn-info',
                             action: function () {
                                 var destinationPath = '{!! url('user/add-timeline-schedule')!!}';
                                 $.ajax({
                                     url: destinationPath,
                                     type: 'POST',
                                     data: {
                                         "_token": '{{ csrf_token() }}',
                                         id: id,
                                         name: scheduleName,
                                         note: note,
                                         date: date,
                                         time: time,
                                         duration: duration,
                                         followup_required: follow_up
                                     },
                                 }).done(function (res) {
                                     toastr.success('A task scheduled successfully');
                                     resetSchedule();
                                     loadPage();
                                 }).fail(function (err) {
   
                                 }).always(function (com) {
   
                                 });
                             }
                         },
                         cancel: function () {
                           toastr.warning('Operation canceled!!');
                             $.alert('Operation <strong>canceled</strong>');
                         }
                     }
                 });
             }
         }
     });
   
     /*--------------------------------Tab Schedule End ------------------------------*/
 
     $('.ks-izi-modal-trigger1').on('click',function(){
         var enquiryId= $(this).attr('enquiry-id');
         window.parent.$('#ks-izi-modal-large1').modal('show');
         window.top.editEnquiry(enquiryId);
     })
     $('.enquiry-delete').on('click',function(){
         var enquiryId= $(this).attr('enquiry-id');
         window.top.deleteEnquiry(enquiryId);
     })
   
   });
   
   function loadPage() {
     setTimeout("location.reload(true);", 1000);
   }
        $(document).ready(function () {
            $('#edit_spin').hide();
   
            /*----------- Front End Scripts ----------*/
   
            $(".nav-tabs a").click(function () {
                var position = $(this).parent().position();
                var width = $(this).parent().width();
                $(".slider").css({
                    "left": +position.left,
                    "width": width
                });
            });
            var actWidth = $(".nav-tabs").find(".active").parent("li").width();
            var actPosition = $(".nav-tabs .active").position();
            $(".slider").css({
                "left": +actPosition.left,
                "width": actWidth
            });
            $('.select2').select2();
            width: 'resolve'
            $('.summernote-1').summernote({
                height: '215',
                toolbar: [
                    ['style', ['bold', 'italic', 'underline', 'clear']],
                    ['font', ['strikethrough', 'superscript', 'subscript']],
                    ['fontsize', ['fontsize']],
                    ['color', ['color']],
                    ['para', ['ul', 'ol', 'paragraph']],
                    ['height', ['height']]
                ],
                disableDragAndDrop: true
            });
   
            logNoteSummernoteInit();
   
            $('.summernote-3').summernote({
                height: '125',
                toolbar: [
                    ['style', ['bold', 'italic', 'underline', 'clear']],
                    ['font', ['strikethrough', 'superscript', 'subscript']],
                    ['fontsize', ['fontsize']],
                    ['color', ['color']],
                    ['para', ['ul', 'ol', 'paragraph']],
                    ['height', ['height']]
                ],
                disableDragAndDrop: true
            });
            /*----------- Front End Scripts ----------*/
            /*----------- Reset Note----------*/
   
            $('#note_reset_values').click(function () {
   
                resetNotes();
            });
   
            function resetNotes() {
                $('#note').val('');
            }
   
            /*----------- Reset Task----------*/
   
            $('#task_reset_values').click(function () {
                resetTask();
            });
   
            function resetTask() {
                $('#note').val('');
            }
   
            /*----------- Reset Schedule----------*/
   
            $('#schedule_reset_values').click(function () {
                resetSchedule();
            });
   
            function resetSchedule() {
                $('#note').val('');
            }
   
            /********************************* Update Additional Field **********************/
            $('.additional_fields').on('change', function () {
                var value = $(this).val();
                var id = $('#enqId').val();
                var field_id = $(this).attr('data-field_id');
               
               $.ajaxSetup({
                  headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                  }
               });
               $.post("{!! url('user/update-additional-field')!!}", {
                     value: value,
                     enquiry_id: id,
                     field_id: field_id
                  }, function (data) {
                     if (data.status == true) {
                        toastr.success('Details updated successfully');
                        // loadPage();
                        resetNotes();
                        appendToTimelineStatus(data,'activity');
                     }
                     else{
                        toastr.error('Something went wrong!');
                     }
                  });
            });
            /********************************* End Update Additional Field ******************/
   
            /*--------------------------------Tab notes start------------------------------------*/
   
            /* Add Note */
   
            $('#btn_save_note').on('click', function () {
                var note = $('.summernote-1').val();
                var id = $('#enqId').val();
   
                if (note !== '') {
                    $.post("{!! url('user/add-timeline-note')!!}", {
                        note: note,
                        id: id
                    }, function (data) {
                        if (data.status === true) {
                            $.alert({
                                title: 'Success',
                                type: 'green',
                                content: "A note logged successfully",
                            });
                            $('.summernote-1').val('');
                            loadPage();
                        }
                    });
                } else {
                    $('#summerNote-error').text('* Required Field');
                }
            });
   
   
            /*--------------------------------Tab notes end------------------------------------*/
   
            /*--------------------------------Tab Log Activity Start----------------------------*/
   
            /* Change log type */
            $('#log-type').on('change', function () {
                var logType = $('#log-type').val();
                if (logType === '{{\App\BackendModel\EnquiryFollowup::TYPE_LOG_CALL}}') {
                    $('#log-outcome').show();
                } else {
                    $('#log-outcome').hide();
                }
            });
            /* Change log type */
            $(function () {
               $('[data-toggle="tooltip"]').tooltip()
            })
   
            /* Save logs */
            $('#btn_save_log').on('click', function () {
                 
                var note = $('#log_note').val();
                var id = $('#enqId').val()
                var logType = $('#log-type').val();
                var logOutcome = $('#log-outcome-val').val();
                var date = $('#log_date').val();
                var time = $('#log_time').val();
   
                var url = "{!! url('user/add-timeline-log')!!}";
               
                 if (note === "") {
                    $('#log-note-error').text(' * Required Field');
                    $('#log-time-error').text('');
                    $('#log-outcome-error').text('');
                    $('#log-date-error').text('');
                    return false;
                }
                if (logType !== '{{\App\BackendModel\EnquiryFollowup::TYPE_LOG_CALL}}' && note === "") {
                    $('#log-note-error').text(' * Required Field');
                }
                if (logType === '{{\App\BackendModel\EnquiryFollowup::TYPE_LOG_CALL}}' && logOutcome !== '') {
                    $.post(url, {
                        enquiry_id: id,
                        log_type: logType,
                        answer: logOutcome,
                        note: note,
                        date: date,
                        time: time,
                    }, function (data) {
                        if (data.status === true) {
                            $.alert({
                                title: 'Success',
                                type: 'green',
                                content: "A call logged successfully",
                            });
   
                            resetLogNoteForm();
                            // loadPage();
                            appendToTimelineStatus(data,'log');
                            loadPage();
                        }
                    });
                } else if (logType !== '' && note !== '') {
                    $.post(url, {
                        enquiry_id: id,
                        log_type: logType,
                        answer: logOutcome,
                        note: note,
                        date: date,
                        time: time,
                    }, function (data) {
                        if (data.status === true) {
                            $.alert({
                                title: 'Success',
                                type: 'green',
                                content: "A log added successfully",
                            });
   
                            resetLogNoteForm();
                            // loadPage();
                            appendToTimelineStatus(data,'log');
                            loadPage();
                        }
                    })
                }
            });
            /*--------------------------------Tab Log Activity End------------------------------*/
   
   
            /*--------------------------------Tab Schedule Start ------------------------------*/
            $('#btn_save_schedule').on('click', function () {
                $('#btn_save_schedule').html("saving");
                $('#btn_save_schedule').prop('disabled', true);
                scheduleName = $('#schedule-name').val();
                note = $('#txtEditor-4').val();
                duration = $('#duration').val();
                time = $('#time-3').val();
                id = $('#enqId').val();
                date = $('#datepicker-3').val();
                var follow_up = $('#follow_up').val();
                if (scheduleName === "") {
                    $('#schedule-name-error').text('* Required Field')
                    $('#txtEditor-4-error').text('')
                    $('#duration-error').text('')
                    $('#datepicker-3-error').text('')
                    $('#time-3-error').text('') -
                    $('#btn_save_schedule').html("Save");
                    $('#btn_save_schedule').prop('disabled', false);
                } else if (duration === "") {
                    $('#duration-error').text('* Required Field')
                    $('#schedule-name-error').text('')
                    $('#txtEditor-4-error').text('')
                    $('#datepicker-3-error').text('')
                    $('#time-3-error').text('')
                    $('#btn_save_schedule').html("Save");
                    $('#btn_save_schedule').prop('disabled', false);
                } else if (date === "") {
                    $('#datepicker-3-error').text('* Required Field')
                    $('#duration-error').text('')
                    $('#schedule-name-error').text('')
                    $('#txtEditor-4-error').text('')
                    $('#time-3-error').text('')
                    $('#btn_save_schedule').html("Save");
                    $('#btn_save_schedule').prop('disabled', false);
                } else if (time === "") {
                    $('#time-3-error').text('* Required Field')
                    $('#txtEditor-4-error').text('* Required Field')
                    $('#datepicker-3-error').text('')
                    $('#duration-error').text('')
                    $('#schedule-name-error').text('')
                    $('#btn_save_schedule').html("Save");
                    $('#btn_save_schedule').prop('disabled', false);
                } else if (note === "") {
                    $('#txtEditor-4-error').text('* Required Field')
                    $('#datepicker-3-error').text('')
                    $('#duration-error').text('')
                    $('#schedule-name-error').text('')
                    $('#time-3-error').text('')
                    $('#btn_save_schedule').html("Save");
                    $('#btn_save_schedule').prop('disabled', false);
                } else if (scheduleName != '' && note != '' && duration != '' && time != '' && date != '') {
                    $.post("{!! url('user/add-timeline-schedule')!!}", {
                        id: id,
                        name: scheduleName,
                        note: note,
                        date: date,
                        time: time,
                        duration: duration,
                        followup_required: follow_up
                    }, function (data) {
                        $.alert({
                            title: 'Success',
                            type: 'green',
                            content: "A task scheduled successfully",
                        });
                        resetSchedule();
                        loadPage();
                    });
                }
            });
   
            /*--------------------------------Tab Schedule End ------------------------------*/
        });
   
          $('.mail-templete').on('click', function (e) {
   
              if(document.getElementById("mail_templete").checked)
              {
                  $('#withouttemplete').hide();
                  $('#templete_button').show();
                  $('#templete-view').show();
                  BASE_URL = window.location.origin;
                  $.ajax({
                        url: BASE_URL + '/user/get-default-email-templates',
                        type: 'GET',
                        dataType: 'JSON',
   
                        data: '',
                        contentType: false,
                        processData: false,
                  }).done(function (res) {
                        console.log(res);
                        $('#email_template_codes').html(res.data.email_template_code);
                        $('#templete-id').val(res.data.id);
                    
                   })
                  .fail(function () {
                  })
                  .always(function (com) {
                    //   $('#enquiry-info-table').DataTable().ajax.reload(null, false);
                  });
                  
              }
              else
              {
                  $('#withouttemplete').show();
                  $('#templete_button').hide();
                  $('#templete-view').hide();
              }
   
          });
          $(document).on('submit', '#sendMailTemplete', function (event) {
              $('#addmail').html("sending");
              $('#addmail').prop('disabled', true);
              event.preventDefault();
              $('.error').html('');
              $('.error').hide();
              id = $('#enqId').val();
              BASE_URL = window.location.origin;
   
              $.ajax({
                  url: BASE_URL + '/user/timeline-send-save-email/' + id,
                  type: 'POST',
                  dataType: 'JSON',
   
                  data: new FormData(this),
                  contentType: false,
                  processData: false,
              }).done(function (res) {
                  console.log(res);
                  if (res.status === 'success') {
                      $('#addmail').html("Send");
                      $('#addmail').prop('disabled', false);
                      $.alert({
                          title: 'Success',
                          type: 'green',
                          content: res.msg,
                      });
                      resetNotes();
                      loadPage();
                  } else if (res.status === 'fail') {
                      $('#addmail').html("Send");
                      $('#addmail').prop('disabled', false);
                      $.alert({
                          title: 'Fail',
                          type: 'red',
                          content: res.msg,
                      });
                      resetNotes();
                      loadPage();
                  } else {
   
                      $.each(res.msg, function (index, val) {
   
                          console.log(index);
                          $('#addmail').html("Send");
                          $('#addmail').prop('disabled', false);
                          $("#sendMail")[0].reset();
   
                          console.log(val);
                          console.log(index);
                          $('.' + index).html(val);
                          $('.' + index).show();
                      });
   
                  }
              })
                  .fail(function () {
                  })
                  .always(function (com) {
                      $('#enquiry-info-table').DataTable().ajax.reload(null, false);
   
                  });
          });
   
   
          $(document).on('click', '#task_edit_btn', function(e) {
            id = $(this).data('taskid');
            $.get("{!! url('user/tasks')!!}" + '/' + id, function(data) {
               $('#task-name-edit').val(data.data.name);
               $('#task-type-edit').val(data.data.task_category_id);
               $('.time_edit').text(data.data.time_field);
               $('#assigned-to-edit').val(data.data.assigned_to);
               var dateValue = new Date(data.data.scheduled_date_edit);
               $('#datepicker1').datepicker('setDate', dateValue);
               $('#task-description-edit').val(data.data.description);
               $('#tskId').val(id);
               $('.select2').select2();
               $('.time_edit').timepicker('setTime', data.data.time_field);

               $('#task-lead-edit').modal('toggle')
            });
         })
		  		  
          function resetTask() {
            $('#txtEditor-3').parent().find('div .Editor-editor').text('');
            $('#time-2').val('');
            $('#datepicker-2').val('');
            $('#task-name').val('');
            $('#reminder').val('');
            }
          $('#btn_submit_edit').on('click', function () {
                taskName = $('#task-name-edit').val();
                description = $('#task-description-edit').val();
                taskType = $('#task-type-edit').val();
                assignedTo = $('#assigned-to-edit').val();
                id = $('#enqId').val();
                task_id = $('#tskId').val();
                time = $('.time_edit').val();
                date = $('#datepicker1').val();
                if (taskName === "") {
                    $('#task-name-error').text('* Required Field')
                    $('#task-description-error').text('');
                    $('#datepicker-2-error').text('');
                    $('#time-2-error').text('');
                    $('#assigned-to-error').text('');
                    $('#task-type-error').text('');
                    $('#reminder-error').text('');
                    $('#btn_save_task').html("Save");
                    $('#btn_save_task').prop('disabled', false);
                    return false;
                } else if (date === "") {
                    $('#datepicker-2-error').text('* Required Field');
                    $('#task-name-error').text('');
                    $('#task-description-error').text('');
                    $('#time-2-error').text('');
                    $('#assigned-to-error').text('');
                    $('#task-type-error').text('');
                    $('#reminder-error').text('');
                    $('#btn_save_task').html("Save");
                    $('#btn_save_task').prop('disabled', false);
                    return false;
   
                } else if (time === "") {
                    $('#time-2-error').text('* Required Field');
                    $('#task-name-error').text('');
                    $('#datepicker-2-error').text('');
                    $('#task-description-error').text('');
                    $('#assigned-to-error').text('');
                    $('#task-type-error').text('');
                    $('#reminder-error').text('');
                    $('#btn_save_task').html("Save");
                    $('#btn_save_task').prop('disabled', false);
                    return false;
                } else if (description === "") {
                    $('#task-description-error').text('* Required Field');
                    $('#task-name-error').text('');
                    $('#datepicker-2-error').text('');
                    $('#time-2-error').text('');
                    $('#assigned-to-error').text('');
                    $('#task-type-error').text('');
                    $('#reminder-error').text('');
                    $('#btn_save_task').html("Save");
                    $('#btn_save_task').prop('disabled', false);
                    return false;
                } else if (taskType === "") {
                    $('#task-type-error').text('* Required Field');
                    $('#task-name-error').text('');
                    $('#datepicker-2-error').text('');
                    $('#time-2-error').text('');
                    $('#assigned-to-error').text('');
                    $('#task-description-error').text('');
                    $('#reminder-error').text('');
                    $('#btn_save_task').html("Save");
                    $('#btn_save_task').prop('disabled', false);
                    return false;
                }
                if (taskName != '' && description != '' && date != '' && time != '') {
                  $('#btn_save_task').html("updating");
                  $('#btn_save_task').prop('disabled', true).css('opacity',.5);
                    $.post("{!! url('user/update-timeline-task')!!}"+'/'+id, {
                        id: id,
                        description: description,
                        name: taskName,
                        task: taskType,
                        assigned_to: assignedTo,
                        date: date,
                        time: time,
                        task_id:task_id
                    }, function (data) {
                     if(data.status){
                        $.alert({
                            title: 'Success',
                            type: 'green',
                            content: "A task updated successfully",
                        });
                        resetTask();
                        loadPage();
                     }else{
                        $.alert({
                            title: 'Warning',
                            type: 'red',
                            content: "Already call task exist",
                        });
                     }
                      
                        
                    });
                }
            });
   
   
      /* -------- Start Purpose vise additional field ----- */
         $('.enquiryPurposeSelect').on('change',function(){
               value = $(this).val();
               getAdditionalData(value);
        });
   
      /* -------- Start Purpose vise additional field ----- */
        function getAdditionalData(value){
            BASE_URL = window.location.origin;
            $.ajax({
                url: BASE_URL + '/user/check-additional-field-purpose/'+value,
                type: 'GET',
                dataType: 'JSON',
            })
            .done(function(res) {
                if(res.status){
                    res.data.checked.forEach(element => {
                        $('.prps_'+element).removeClass('d-none');
                    });
                    res.data.unchecked.forEach(element => {
                        $('.prps_'+element).addClass('d-none');
                    });
                }
   
            })
            .fail(function() {
            });
        }
   
         $(document).on('click', '#reschedule_plan', function (event) {
            $('#re_task_id').val($(this).attr('task-id'))
            $('#reschedule_tasks_modal').modal('show');
         });
   
   
         $(document).on('click', '#task_complete', function (event) {
            id = $(this).attr('task-id');
             $.confirm({
                title: 'Status',
                content: 'Are you sure want to complete the task ?',
                icon: 'la la-question-circle',
                animation: 'scale',
                closeAnimation: 'scale',
                opacity: 0.5,
                buttons: {
                    'confirm': {
                        text: 'Proceed',
                        btnClass: 'btn-info',
                        action: function () {
                            $.ajaxSetup({
                                headers: {
                                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                                }
                            });
                              $.ajax({
                                 url: BASE_URL + '/user/complete-multiple-tasks/'+id,
                                 type: 'POST',
                                 dataType: 'JSON',
                                 contentType: false,
                                 processData: false,
                              }).done(function (res) {
                                 if (res.status == 'success') {
                                       toastr.success(res.msg);
                                 } else {
                                       $.each(res.msg, function (index, val) {
                                          $('.' + index).html(val);
                                          $('.' + index).show();
                                       });
                                 }
                              }).fail(function () {
                              }).always(function (com) {
                                 loadPage()
                              });
                        }
                    },
                    cancel: function () {
                        toastr.warning('Operation canceled!!');
                    }
                }
            });
         });
   
         // Task reschedule event
         $(document).on('submit', '#taskReschedule', function (event) {
            event.preventDefault();
            $('.error').html('');
            $('.error').hide();
            var id = $('#re_task_id').val();
            $.ajax({
               url: BASE_URL + '/user/tasks/reschedule/' + id,
               type: 'POST',
               dataType: 'JSON',
               data: new FormData(this),
               contentType: false,
               processData: false,
            }).done(function (res) {
               if (res.status == true) {
                     $('#reschedule_tasks_modal').modal('hide');
                     $.alert({
                        title: 'Success',
                        type: 'green',
                        content: res.msg,
                     });
               } else {
                     $.each(res.msg, function (index, val) {
                        $('.' + index).html(val);
                        $('.' + index).show();
                     });
               }
            }).fail(function () {
            }).always(function (com) {
               loadPage()
            });
         });

         $('.delete-task').on('click', function (e) {
                e.preventDefault();
                var id = $(this).data('task-id');
                var destinationPath = '{{url('user/timeline-delete-follow-up')}}' + '/' + id;
                $.confirm({
                    title: 'Deletion',
                    content: 'Are you sure you want to delete ?',
                    icon: 'la la-question-circle',
                    animation: 'scale',
                    closeAnimation: 'scale',
                    opacity: 0.5,
                    buttons: {
                        'confirm': {
                            text: 'Proceed',
                            btnClass: 'btn-info',
                            action: function () {
                                $.ajax({
                                    url: destinationPath,
                                    type: 'DELETE',
                                    data: {
                                        "_token": '{{ csrf_token() }}',
                                    },
                                }).done(function (res) {
                                    if (res.status === true) {
                                        $.alert({
                                            title: 'Success',
                                            type: 'green',
                                            content: res.msg,
                                        });
                                        loadPage();
                                        $("#time_line_" + fId).remove();
                                        
                                    } else {
                                        $.alert({
                                            title: 'Failed',
                                            type: 'red',
                                            content: res.msg,
                                        });
                                    }
                                }).fail(function (err) {
   
                                }).always(function (com) {
   
                                });
                            }
                        },
                        cancel: function () {
                            $.alert('Operation <strong>canceled</strong>');
                        }
                    }
                });
            })
    </script>

    <script>

   $(document).on('click', '#addEnquiry' ,function(event){
       $('#myModal2').modal('toggle');
       $('input.datepicker').next('.input-group-append').show();
   })
    </script>

    <script>

      function form_submit() {
         document.getElementById("msform").submit();
      }  
      
    </script>
    @if (session('create_deal_error'))      
    <script type="text/javascript">
       $('#myModal2').modal('toggle');
       
       function editEnquiry(enquiryId){
           $.ajax({
               url: BASE_URL + '/user/enquiries/' + enquiryId,
               type: 'GET',
               dataType: 'JSON',
           }).done(function (res) {
               $('#more_numbers_a').html("");
               if (res.status === "success") {
                   data = res.data;
                   $("[name='pk_int_enquiry_id']").val(data.pk_int_enquiry_id);
                   enquiryId = data.pk_int_enquiry_id;
                   $("[name='staff_id']").val(data.staff_id);
                   $("[name='district_id']").val(data.district_id);
                //    getTaluks(data.district_id, data.taluk_id);
                   enquiryId = data.pk_int_enquiry_id;
                   $("[name='vchr_customer_name']").val(data.vchr_customer_name);
                   $("[name='vchr_customer_company_name']").val(data.vchr_customer_company_name);
                   $("[name='vchr_customer_email']").val(data.vchr_customer_email);
                   $("[name='vchr_customer_mobile']").val(data.mobile_no);
                   $("[name='country_code']").val(data.country_code);
                   $("[name='landline_number']").val(data.landline_number);
                   $("[name='purchase_plan']").val(data.purchase_plan);
                   $("[name='live_deal']").val(data.live_deal);
                   $("[name='model_id']").val(data.model_id);
   
                   if (data.purchase_plan == 'Yes') {
                       document.getElementById('div_date_of_purchase').style.display = "block";
                       document.getElementById('div_remarks').style.display = "none";
                       $("[name='date_of_purchase']").val(data.date_of_purchase);
                   } else if (data.purchase_plan == 'No') {
                       document.getElementById('div_date_of_purchase').style.display = "none";
                       document.getElementById('div_remarks').style.display = "block";
                       $("[name='remarks']").val(data.remarks);
                   }
   
                   if (data.live_deal == 'Yes') {
                       document.getElementById('div_competing_model').style.display = "block";
                       getCompetingModels(data.model_id, data.competing_model)
                       //    $("[name='competing_model']").val(data.competing_model);
   
                   } else if (data.live_deal == 'No') {
   
                   }
   
                   $.each(data.more_phone_numbers, function (key, value) {
   
                       var content = '<div class="row m-0">' +
                           '<input type="text" class="form-control col-md-11"  name="more_phone_numbers[' +
                           key + ']" required multiple="" value="' + value +
                           '" placeholder="Mobile number" onkeypress="return /[0-9]/i.test(event.key)"/>' +
                           '<a href="#" class="remove_field ml-1 mt-0 ">' +
                           '<i class="fa fa-minus-circle" style="font-size:14px;color:red">' +
                           '</i>' + '</a>' +
                           '</div>';
                       $('#more_numbers_a').append(content);
                       $('#more_numbers_a').on("click", ".remove_field", function () {
                           $(this).closest(".row").remove();
                       });
                   });
                   $('#add_details').html('');
                   var add_content = '';
                   $.each(data.additional_details, function (key, value) {
                    var status = (value.additional_purpose_count > 0) ? 'd-none' : ''; 
                       if (value.input_type == 1) {
                           if (value.is_required == 1) {
                               add_content = add_content + '<div class="md-form mb-1 col-md-6 '+status+' prps_'+value.id+'">' +
                                   '<label>' + value.field_name + '</label>' +
                                   '<div class="form-group">' +
                                   '<input type="text" '+((!value.additional_purpose_count > 0)?'required' : '') +' class="form-control" name="additional_field[' + value.id + ']"' +
                                   'placeholder="' + value.field_name + '" autocomplete="off" value="' + value.info + '">' +
                                   '</div>' +
                                   '</div>';
                           } else {
                               add_content = add_content + '<div class="md-form mb-1 col-md-6 '+status+' prps_'+value.id+'">' +
                                   '<label>' + value.field_name + '</label>' +
                                   '<div class="form-group">' +
                                   '<input type="text" class="form-control" name="additional_field[' + value.id + ']"' +
                                   'placeholder="' + value.field_name + '" autocomplete="off" value="' + value.info + '">' +
                                   '</div>' +
                                   '</div>';
                           }
                       } else if (value.input_type == 2) {
                           if (value.is_required == 1) {
                               add_content = add_content + '<div class="md-form mb-1 col-md-6 '+status+' prps_'+value.id+'">' +
                                   '<label for="default-input" class="form-control-label">' + value.field_name + '</label>' +
                                   '<select '+((!value.additional_purpose_count > 0)?'required' : '') +' class="form-control" name="additional_field[' + value.id + ']"id="enquiryTypeSelect">' +
                                   '<option value="">Select ' + value.field_name + '</option>';
                               $.each(value.values, function (key, valueI) {
                                   if (valueI == value.info) {
                                       add_content = add_content + '<option value = "' + valueI + '" selected>' + valueI + '</option>';
                                   } else {
                                       add_content = add_content + '<option value = "' + valueI + '">' + valueI + '</option>';
                                   }
   
                               });
                               add_content = add_content + '</select></div>';
                           } else {
                               add_content = add_content + '<div class="md-form mb-1 col-md-6 '+status+' prps_'+value.id+'">' +
                                   '<label for="default-input" class="form-control-label">' + value.field_name + '</label>' +
                                   '<select class="form-control" name="additional_field[' + value.id + ']" id="enquiryTypeSelect">' +
                                   '<option value="">Select ' + value.field_name + '</option>';
                               $.each(value.values, function (key, valueI) {
                                   if (valueI == value.info) {
                                       add_content = add_content + '<option value = "' + valueI + '" selected>' + valueI + '</option>';
                                   } else {
                                       add_content = add_content + '<option value = "' + valueI + '">' + valueI + '</option>';
                                   }
   
                               });
                               add_content = add_content + '</select></div>';
                           }
                       } else if (value.input_type == 8) {//Multi Select

                            if (value.is_required == 1) {
                                add_content = add_content + '<div class="md-form mb-1 col-md-6 '+status+' prps_'+value.id+'">' +
                                   '<label for="default-input" class="form-control-label">' + value.field_name + '</label>' +
                                   '<select '+(value.is_required==1 ? '' : ((!value.additional_purpose_count > 0)?'required' : '') )+' multiple class="form-control select2" style="width:100%" name="additional_field[' + value.id + '][]" id="enquiryTypeSelect">' +
                                   '<option value="">Select' + value.field_name + '</option>';
                               $.each(value.values, function (key, valueI) {
                                   if (value.info!='' && JSON.parse(value.info).includes(valueI )) {
                                       add_content = add_content + '<option value = "' + valueI + '" selected>' + valueI + '</option>';
                                   } else {
                                       add_content = add_content + '<option value = "' + valueI + '">' + valueI + '</option>';
                                   }
   
                               });
                               add_content = add_content + '</select></div>';
                           } else {
                              add_content = add_content + '<div class="md-form mb-1 col-md-6 '+status+' prps_'+value.id+'">' +
                                   '<label for="default-input" class="form-control-label">' + value.field_name + '</label>' +
                                   '<select multiple class="form-control select2" style="width:100%" name="additional_field[' + value.id + '][]" id="enquiryTypeSelect">' +
                                   '<option value="">Select' + value.field_name + '</option>';
                               $.each(value.values, function (key, valueI) {
                                   if (value.info!='' && JSON.parse(value.info).includes(valueI )) {
                                       add_content = add_content + '<option value = "' + valueI + '" selected>' + valueI + '</option>';
                                   } else {
                                       add_content = add_content + '<option value = "' + valueI + '">' + valueI + '</option>';
                                   }
   
                               });
                               add_content = add_content + '</select></div>';
                           }
                       }else if (value.type_text=='Image') {//Image
                               add_content = add_content + '<div class="md-form mb-1 col-md-6 '+status+' prps_'+value.id+'">' +
                                   '<label for="default-input" class="form-control-label">' + value.field_name + '</label>' +
                                   '<input '+(value.is_required==1 ? 'required' : '')+' type="file"  class="form-control" style="width:100%" name="additional_field[' + value.id + ']" id="enquiryTypeSelect"></div>'+
                                   '<img src="'+value.info+'" width="50" height="50" class="img">';
                       }else if (value.input_type==7) {//Phone  number
                        if (value.is_required == 1) {
                               add_content = add_content + '<div class="md-form mb-1 col-md-6 '+status+' prps_'+value.id+'">' +
                                   '<label>' + value.field_name + '</label>' +
                                   '<div class="form-group">' +
                                   '<input type="number" '+((!value.additional_purpose_count > 0)?'required' : '') +' class="form-control" name="additional_field[' + value.id + ']"' +
                                   'placeholder="' + value.field_name + '" autocomplete="off" value="' + value.info + '">' +
                                   '</div>' +
                                   '</div>';
                           } else {
                               add_content = add_content + '<div class="md-form mb-1 col-md-6 '+status+' prps_'+value.id+'">' +
                                   '<label>' + value.field_name + '</label>' +
                                   '<div class="form-group">' +
                                   '<input type="number" class="form-control" name="additional_field[' + value.id + ']"' +
                                   'placeholder="' + value.field_name + '" autocomplete="off" value="' + value.info + '">' +
                                   '</div>' +
                                   '</div>';
                           }
                       }
                        else {
                           if (value.is_required == 1) {
                               add_content = add_content + '<div class="md-form mb-1 col-md-6 '+status+' prps_'+value.id+'">' +
                                   '<label>' + value.field_name + '</label>' +
                                   '<div class="form-group">' +
                                   '<input '+((!value.additional_purpose_count > 0)?'required' : '') +' type="date" class="form-control" name="additional_field[' + value.id + ']"' +
                                   'placeholder="' + value.field_name + '" autocomplete="off" value="' + value.info + '">' +
                                   '</div>' +
                                   '</div>';
                           } else {
                               add_content = add_content + '<div class="md-form mb-1 col-md-6 '+status+' prps_'+value.id+'">' +
                                   '<label>' + value.field_name + '</label>' +
                                   '<div class="form-group">' +
                                   '<input type="date" class="form-control" name="additional_field[' + value.id + ']"' +
                                   'placeholder="' + value.field_name + '" autocomplete="off" value="' + value.info + '">' +
                                   '</div>' +
                                   '</div>';
                           }
                       }
                       //
                   });
                   $('#add_details').append(add_content);
                   $('.select2').select2();
                   $('#enquiryTypeSelectMuliple').select2().trigger('change') ;
                   $("[name='more_phone_numbers']").val(data.more_phone_numbers);
                   $("[name='vchr_enquiry_feedback']").val(data.vchr_enquiry_feedback);
                   $("[name='fk_int_enquiry_type_id']").val(data.fk_int_enquiry_type_id);
                   $("[name='fk_int_purpose_id']").val(data.fk_int_purpose_id);
                   $("[name='date_of_birth']").val(data.date_of_birth);
                   $("[name='feedback_status']").val(data.feedback_status);
                   // var obj = JSON.parse(data.address)
                   // console.log('address');
                   // console.log(data.address);
                   // $("[name='address']").val(obj.customer_address);
                   $("[name='address']").val(data.address);
                   $("[name='designation_id']").val(data.designation_id);
                   $("[name='lead_type_id']").val(data.lead_type_id);
                   $("[name='purchase_date']").val(data.purchase_date);
                   $("[name='exp_wt_grams']").val(data.exp_wt_grams);
                   $("[name='function_date']").val(data.function_date);
                   // var more_phone_numbers = JSON.stringify(jsonList.list, function (key, value) {
                   //     return (value === undefined) ? "" : value
                   // });
   
               getAdditionalData(data.fk_int_purpose_id);
               $("#enquirySubmit")[0].reset();
               $('.error').html('');
               $('.error').hide();
           }
       }).fail(function () {
       }).always(function () {
       });
   }
    </script>
    @endif

	
<script>

// LOG NOTE EDIT----------------------------------------------------------

   $(document).on('click','.log_note_edit_btn',function(e){
		id = $(this).data('lnoteid');
		$.get("{!! url('user/edit-lead-log-note')!!}"+'/'+id, 
		function (data) 
		{
		   $('#LogNoteId').val(id);
		   $('#log-type-edit').select2().val(data.data.log_type).trigger("change");
		   $("#lead-log-note-edit .note-editable").html(data.data.note);
		});
			
    });
	

$('#btn_update_log').on('click', function () {
         $('#btn_update_log').html("updating");
         $('#btn_update_log').prop('disabled', true);
         log_noteid = $('#LogNoteId').val();
         log_note = $("#lead-log-note-edit .note-editable").html();
         log_type=$('#log-type-edit').val();

		 if (log_noteid != '' && log_note != '' && log_type!='') {
             
			 $.post("{!! url('user/update-lead-log-note')!!}", 
			 {
                 log_note_id: log_noteid,
                 log_note: log_note,
                 log_type: log_type,
             }, function (data)
			 {
				 if(data.status==true)
				 {
					 $("#lead-log-note-edit").fadeOut(500);
					 
					 $.alert({
						 title: 'Success',
						 type: 'green',
						 content: data.msg,
					 });
                 loadPage();
				 }
				 else
				 {
					 $.alert({
						 title: 'Fail',
						 type: 'red',
						 content: data.msg,
					 });
				 }
             });
         }
     });


// DELETE LOG NOTE ---------------------------------------------

 $('.btn_delete_lead_log_note').on('click', function (e) {
         e.preventDefault();
         var lnoteId = $(this).attr('data-lnoteid');
         var destinationPath = "{{url('user/delete-lead-log-note')}}" + "/" + lnoteId;
         $.confirm({
             title: 'Deletion',
             content: 'Are you sure you want to delete ?',
             icon: 'la la-question-circle',
             animation: 'scale',
             closeAnimation: 'scale',
             opacity: 0.5,
             buttons: {
                 'confirm': {
                     text: 'Proceed',
                     btnClass: 'btn-info',
                     action: function () {
                         $.ajax({
                             url: destinationPath,
                             type: 'DELETE',
                             data: {
                                 "_token": '{{ csrf_token() }}',
                             },
                         }).done(function (res) {
                             if (res.status === true) {
                                 $.alert({
                                     title: 'Success',
                                     type: 'green',
                                     content: res.msg,
                                 });
                                 loadPage();
                             } else {
                                 $.alert({
                                     title: 'Failed',
                                     type: 'red',
                                     content: res.msg,
                                 });
                             }
                         }).fail(function (err) {
   
                         }).always(function (com) {
   
                         });
                     }
                 },
                 cancel: function () {
                     $.alert('Operation <strong>canceled</strong>');
                 }
             }
         });
     });

     $('.click-to-call').on('click', function (e) {
         e.preventDefault();
         var enquiryId = $(this).attr('enquiry-id');
         var mobileNo = $(this).attr('data-mobile');
         var destinationPath = "{{url('user/web-click-to-call')}}";
         $.confirm({
             title: 'Click to call',
             content: 'Are you sure ?',
             icon: 'la la-question-circle',
             animation: 'scale',
             closeAnimation: 'scale',
             opacity: 0.5,
             buttons: {
                 'confirm': {
                     text: 'Proceed',
                     btnClass: 'btn-info',
                     action: function () {
                        $('#loadingDivWeb').removeClass('d-none')
                         $.ajax({
                             url: destinationPath,
                             type: 'POST',
                             data: {
                                 "_token": '{{ csrf_token() }}',
                                 "enquiry_id" : enquiryId,
                                 "phone_number" : mobileNo
                             },
                         }).done(function (res) {
                             if (res.status != 0) {
                                 // $.alert({
                                 //     title: 'Success',
                                 //     type: 'green',
                                 //     content: res.message,
                                 // });
                             } else {
                                 $.alert({
                                     title: 'Failed',
                                     type: 'red',
                                     content: res.message,
                                 });
                                 $('#loadingDivWeb').addClass('d-none');
                             }
                         }).fail(function (err) {
   
                         }).always(function (com) {
                           setTimeout(function() {
                              $('#loadingDivWeb').addClass('d-none');
                           }, 3000); 
                         });
                     }
                 },
                 cancel: function () {
                     $.alert('Operation <strong>canceled</strong>');
                 }
             }
         });
     });


      const iconupdown = document.querySelector('.iconupdown');
      const enquiryIcon = document.querySelector('.enquiry-icon');
      const enquiryList = document.querySelector('.enquiry-list');

      enquiryIcon.addEventListener('click', () => {
      enquiryList.style.display = enquiryList.style.display === 'none' ? 'block' : 'none';
      iconupdown.classList.toggle('fa-chevron-down');
      iconupdown.classList.toggle('fa-chevron-up');
      });

</script>
	
	
@endpush