<?php

use App\Common\Variables;
use Getlead\Rolespermissions\Models\UserPermission;
use Illuminate\Database\Seeder;

class TimelineActivitiesPermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $timeline_permissions = [
            'Timeline Activities' => [
                'View Tasks' => 'timeline.view.tasks',
                'View Deals' => 'timeline.view.deals', 
                'View Activities' => 'timeline.view.activities',
                'View Log Notes' => 'timeline.view.log_notes',
                'View Orders' => 'timeline.view.orders'            ],
        ];

        $this->storePermissions($timeline_permissions, Variables::SERVICE_CRM);
    }

    function storePermissions($permissions, $module)
    {
        foreach ($permissions as $title => $permission) {
            foreach ($permission as $name => $route) {
                // Check if permission already exists
                $existingPermission = UserPermission::where('route', $route)->first();
                
                if (!$existingPermission) {
                    UserPermission::create([
                        'module' => $module,
                        'title' => $title,
                        'name' => $name,
                        'route' => $route
                    ]);
                }
            }
        }
    }
}
