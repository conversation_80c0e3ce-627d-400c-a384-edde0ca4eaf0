<?php

namespace Database\Seeders;

use App\Common\Variables;
use Getlead\Rolespermissions\Models\UserPermission;
use Illuminate\Database\Seeder;

class TimelineActivitiesPermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $timeline_permissions = [
            'Timeline Activities' => [
                'View Tasks' => 'timeline.view.tasks',
                'View Deals' => 'timeline.view.deals', 
                'View WhatsApp' => 'timeline.view.whatsapp',
                'View Calls' => 'timeline.view.calls',
                'View Notes' => 'timeline.view.notes',
                'View IVR' => 'timeline.view.ivr',
                'View Visits' => 'timeline.view.visits',
                'View Emails' => 'timeline.view.emails',
                'View Voice Notes' => 'timeline.view.voice_notes',
                'View Lead Updates' => 'timeline.view.lead_updates'
            ],
        ];

        $this->storePermissions($timeline_permissions, Variables::SERVICE_CRM);
    }

    function storePermissions($permissions, $module)
    {
        foreach ($permissions as $title => $permission) {
            foreach ($permission as $name => $route) {
                UserPermission
                // Check if permission already exists
                $existingPermission = UserPermission::where('route', $route)->first();
                
                if (!$existingPermission) {
                    UserPermission::create([
                        'module' => $module,
                        'title' => $title,
                        'name' => $name,
                        'route' => $route
                    ]);
                }
            }
        }
    }
}
